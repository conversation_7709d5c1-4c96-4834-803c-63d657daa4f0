/**
 * Article Type Intelligence System - 2025 Enhanced Content Analysis
 * 
 * This system provides comprehensive article type detection and optimization
 * based on 2025 content writing best practices and competitive analysis.
 */

import { GeminiService } from '../../gemini';

export interface ArticleTypeAnalysis {
  primaryType: string;
  confidence: number;
  secondaryType?: string;
  reasoning: string;
  structure: {
    sections: string[];
    wordCountRange: number[];
    keyElements: string[];
  };
  engagementStrategies: string[];
  seoConsiderations: string[];
  visualElements: string[];
  competitorAnalysisNeeded: string[];
}

export interface ContentGapAnalysis {
  topicGaps: string[];
  depthGaps: string[];
  formatGaps: string[];
  audienceGaps: string[];
  successPatterns: {
    structuralElements: string[];
    engagementTactics: string[];
    qualityIndicators: string[];
    visualStrategies: string[];
  };
  improvementOpportunities: {
    qualityEnhancements: string[];
    uniqueAngles: string[];
    betterOrganization: string[];
    userExperience: string[];
  };
  competitiveIntelligence: {
    averageWordCount: number;
    commonHeadingStructure: string[];
    dataUsagePatterns: string[];
    linkingStrategies: string[];
  };
  actionableInsights: string[];
}

export interface CompetitorStrengthAnalysis {
  contentExcellence: {
    depthFactors: string[];
    credibilitySignals: string[];
    uniqueInsights: string[];
    practicalValue: string[];
  };
  engagementMastery: {
    hookStrategies: string[];
    narrativeFlow: string[];
    visualIntegration: string[];
    ctaOptimization: string[];
  };
  userExperienceSuperiority: {
    organizationMethods: string[];
    readabilityFactors: string[];
    mobileOptimization: string[];
    navigationExcellence: string[];
  };
  seoOptimizationExcellence: {
    keywordIntegration: string[];
    metaOptimization: string[];
    technicalSeo: string[];
    contentFreshness: string[];
  };
  innovationFactors: {
    uniqueFormats: string[];
    presentationMethods: string[];
    technologyIntegration: string[];
    engagementFeatures: string[];
  };
  keySuccessFactors: string[];
  implementationPriority: string[];
}

export class ArticleTypeIntelligence {
  private geminiService: GeminiService;

  constructor() {
    this.geminiService = new GeminiService();
  }

  /**
   * 2025 Article Type Knowledge Base
   */
  private getArticleTypeKnowledge(): Record<string, any> {
    return {
      'listicle': {
        description: 'Numbered lists with engaging hooks and data-driven points',
        bestFor: ['Top X', 'Best', 'Ways to', 'comparison topics'],
        structure: ['Hook', 'List items with explanations', 'Conclusion'],
        engagement: 'High shareability, scannable format',
        wordCountRange: [800, 2500],
        keyElements: ['Numbers in title', 'Bullet points', 'Visual elements', 'Quick takeaways'],
        seoStrength: 'Featured snippets, high CTR',
        examples: ['10 Best Tools for...', '7 Ways to Improve...', 'Top 15 Strategies for...']
      },
      'how-to-guide': {
        description: 'Step-by-step tutorials with actionable content',
        bestFor: ['Process explanations', 'tutorials', 'instructional content'],
        structure: ['Problem identification', 'Solution steps', 'Results/Tips'],
        engagement: 'High utility value, bookmark-worthy',
        wordCountRange: [1500, 4000],
        keyElements: ['Clear steps', 'Screenshots/visuals', 'Prerequisites', 'Troubleshooting'],
        seoStrength: 'Long-tail keywords, high dwell time',
        examples: ['How to Set Up...', 'Complete Guide to...', 'Step-by-Step Tutorial...']
      },
      'product-review': {
        description: 'Comparison-based content with feature analysis',
        bestFor: ['Product comparisons', 'service evaluations', 'buying guides'],
        structure: ['Overview', 'Features analysis', 'Pros/Cons', 'Verdict'],
        engagement: 'Purchase decision support, high conversion',
        wordCountRange: [2000, 3500],
        keyElements: ['Comparison tables', 'Ratings', 'Price analysis', 'User testimonials'],
        seoStrength: 'Commercial intent keywords, affiliate potential',
        examples: ['X vs Y Review', 'Best Products for...', 'Honest Review of...']
      },
      'news-article': {
        description: 'Timely, fact-based content with inverted pyramid structure',
        bestFor: ['Current events', 'announcements', 'industry updates'],
        structure: ['Lead paragraph', 'Body details', 'Background', 'Conclusion'],
        engagement: 'Timeliness and relevance, social sharing',
        wordCountRange: [600, 1500],
        keyElements: ['Who/What/When/Where/Why', 'Quotes', 'Sources', 'Recent data'],
        seoStrength: 'Trending keywords, news snippets',
        examples: ['Company Announces...', 'Industry Report Shows...', 'Breaking: New Study...']
      },
      'opinion-piece': {
        description: 'Argument-driven content with personal perspective',
        bestFor: ['Controversial topics', 'thought leadership', 'industry commentary'],
        structure: ['Thesis statement', 'Supporting arguments', 'Counter-arguments', 'Conclusion'],
        engagement: 'Debate and discussion, thought leadership',
        wordCountRange: [1200, 2500],
        keyElements: ['Strong viewpoint', 'Evidence', 'Personal experience', 'Call to action'],
        seoStrength: 'Brand authority, social engagement',
        examples: ['Why I Believe...', 'The Problem with...', 'My Take on...']
      },
      'case-study': {
        description: 'Problem-solution narrative with data-driven results',
        bestFor: ['Success stories', 'business examples', 'proof of concept'],
        structure: ['Challenge description', 'Solution implementation', 'Results', 'Lessons learned'],
        engagement: 'Credibility and social proof, lead generation',
        wordCountRange: [2000, 4000],
        keyElements: ['Specific metrics', 'Before/after', 'Process details', 'Takeaways'],
        seoStrength: 'Industry authority, long-form content',
        examples: ['How Company X Increased...', 'Case Study: Solving...', 'Success Story:...']
      },
      'research-article': {
        description: 'Data-heavy, citation-rich analytical content',
        bestFor: ['Industry insights', 'trend analysis', 'academic topics'],
        structure: ['Abstract/Summary', 'Methodology', 'Findings', 'Implications'],
        engagement: 'Authority and expertise, professional sharing',
        wordCountRange: [3000, 6000],
        keyElements: ['Data visualization', 'Citations', 'Methodology', 'Statistical analysis'],
        seoStrength: 'E-A-T signals, backlink magnet',
        examples: ['Research: The State of...', 'Analysis: Trends in...', 'Study Reveals...']
      }
    };
  }

  /**
   * Detect optimal article type based on topic and context
   */
  async detectArticleType(topic: string, targetAudience?: string, context?: any): Promise<ArticleTypeAnalysis> {
    const articleTypes = this.getArticleTypeKnowledge();
    
    const prompt = `
ARTICLE TYPE INTELLIGENCE SYSTEM - 2025 ANALYSIS

Analyze the topic "${topic}" and determine the optimal article type and structure.

AVAILABLE ARTICLE TYPES:
${Object.entries(articleTypes).map(([type, info]) => `
${type.toUpperCase()}:
- Description: ${info.description}
- Best for: ${info.bestFor.join(', ')}
- Structure: ${info.structure.join(' → ')}
- Word count: ${info.wordCountRange[0]}-${info.wordCountRange[1]} words
- Key elements: ${info.keyElements.join(', ')}
- SEO strength: ${info.seoStrength}
- Examples: ${info.examples.join(', ')}
`).join('\n')}

ANALYSIS CONTEXT:
- Topic: "${topic}"
- Target audience: ${targetAudience || 'General audience'}
- Current date: ${new Date().toLocaleDateString()} (keep content current for 2025)

ANALYSIS REQUIREMENTS:
1. Primary article type recommendation with confidence score
2. Secondary type if hybrid approach is beneficial
3. Detailed reasoning for the recommendation
4. Specific structural elements for this topic
5. Engagement optimization strategies
6. SEO considerations specific to this article type
7. Visual elements that would enhance the content
8. What to analyze in competitor content

Provide analysis in JSON format:
{
  "primaryType": "article_type_name",
  "confidence": 0.95,
  "secondaryType": "optional_secondary_type",
  "reasoning": "detailed explanation of why this type is optimal",
  "structure": {
    "sections": ["section1", "section2", "section3"],
    "wordCountRange": [min, max],
    "keyElements": ["element1", "element2", "element3"]
  },
  "engagementStrategies": ["strategy1", "strategy2", "strategy3"],
  "seoConsiderations": ["consideration1", "consideration2", "consideration3"],
  "visualElements": ["tables", "charts", "images", "infographics"],
  "competitorAnalysisNeeded": ["what_to_analyze1", "what_to_analyze2"]
}`;

    try {
      const response = await this.geminiService.generateContent(prompt);
      return this.parseJsonResponse(response.response);
    } catch (error) {
      console.error('Article type detection failed:', error);
      return this.getDefaultAnalysis(topic);
    }
  }

  /**
   * Parse JSON response with error handling
   */
  private parseJsonResponse(response: string): any {
    try {
      // Clean the response to extract JSON
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      throw new Error('No JSON found in response');
    } catch (error) {
      console.error('JSON parsing failed:', error);
      return this.getDefaultAnalysis('unknown topic');
    }
  }

  /**
   * Fallback analysis when detection fails
   */
  private getDefaultAnalysis(topic: string): ArticleTypeAnalysis {
    return {
      primaryType: 'how-to-guide',
      confidence: 0.5,
      reasoning: 'Default fallback due to analysis error',
      structure: {
        sections: ['Introduction', 'Main Content', 'Conclusion'],
        wordCountRange: [1500, 2500],
        keyElements: ['Clear structure', 'Actionable content', 'Examples']
      },
      engagementStrategies: ['Strong hook', 'Practical examples', 'Clear takeaways'],
      seoConsiderations: ['Target keywords', 'Meta optimization', 'Internal linking'],
      visualElements: ['Images', 'Bullet points', 'Subheadings'],
      competitorAnalysisNeeded: ['Content structure', 'Word count', 'Key topics covered']
    };
  }

  /**
   * Get article type recommendations based on keyword analysis
   */
  getTypeByKeywords(topic: string): string {
    const keywords = topic.toLowerCase();
    
    if (keywords.includes('how to') || keywords.includes('guide') || keywords.includes('tutorial')) {
      return 'how-to-guide';
    }
    if (keywords.includes('best') || keywords.includes('top') || keywords.match(/\d+/)) {
      return 'listicle';
    }
    if (keywords.includes('review') || keywords.includes('vs') || keywords.includes('comparison')) {
      return 'product-review';
    }
    if (keywords.includes('news') || keywords.includes('announced') || keywords.includes('breaking')) {
      return 'news-article';
    }
    if (keywords.includes('opinion') || keywords.includes('why') || keywords.includes('should')) {
      return 'opinion-piece';
    }
    if (keywords.includes('case study') || keywords.includes('success') || keywords.includes('results')) {
      return 'case-study';
    }
    if (keywords.includes('research') || keywords.includes('study') || keywords.includes('analysis')) {
      return 'research-article';
    }
    
    return 'how-to-guide'; // Default
  }
}
