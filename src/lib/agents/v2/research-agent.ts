/**
 * Research Agent for Invincible v.2 Multi-Agent System
 * Specialized agent responsible for comprehensive research and data gathering
 * Enhanced with temporal awareness and latest fact gathering capabilities
 */

import { TavilySearchService } from '../../search';
import { NodeWebScraperService } from '../../web-scraper';
import { GeminiService } from '../../gemini';
import { KnowledgeBase } from '../../knowledge-base';
import { AgentState, AgentPhase, ResearchAgentConfig } from './types';

export class ResearchAgent {
  private searchService: TavilySearchService;
  private webScraperService: NodeWebScraperService;
  private geminiService: GeminiService;
  private config: ResearchAgentConfig;
  private agentId: string;
  private currentDate: Date;
  private researchPlan: any;
  private iterativeSearchHistory: Array<{query: string, results: any[], insights: string[], refinements: string[]}>;
  private knowledgeGraph: Map<string, any>;

  constructor(config: Partial<ResearchAgentConfig> = {}) {
    this.config = {
      searchDepth: config.searchDepth ?? 10, // Enhanced for deep research
      maxUrls: config.maxUrls ?? 15, // Enhanced for comprehensive coverage
      parallelSearches: config.parallelSearches ?? 4, // Enhanced for speed
      researchQueries: config.researchQueries ?? 12, // Enhanced for depth
      deepResearchEnabled: config.deepResearchEnabled ?? true,
      iterativeRefinement: config.iterativeRefinement ?? true,
      multiStepAnalysis: config.multiStepAnalysis ?? true,
      knowledgeGraphEnabled: config.knowledgeGraphEnabled ?? true
    };

    this.agentId = 'enhanced-research-agent';
    this.searchService = new TavilySearchService();
    this.webScraperService = new NodeWebScraperService();
    this.geminiService = new GeminiService();
    this.currentDate = new Date();
    this.iterativeSearchHistory = [];
    this.knowledgeGraph = new Map();
  }

  /**
   * Get current date and time for temporal context
   */
  private getCurrentTemporalContext(): {
    currentDate: string;
    currentYear: number;
    currentMonth: string;
    timeContext: string;
    searchDateFilters: string[];
  } {
    const now = this.currentDate;
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    
    const currentYear = now.getFullYear();
    const currentMonth = monthNames[now.getMonth()];
    const currentDate = `${currentMonth} ${currentYear}`;
    
    // Create time-based search filters for latest information
    const searchDateFilters = [
      `${currentYear}`,
      `${currentMonth} ${currentYear}`,
      `recent ${currentYear}`,
      `latest ${currentYear}`,
      `current ${currentYear}`,
      `updated ${currentYear}`,
      `new ${currentYear}`,
      `${currentYear} data`,
      `${currentYear} statistics`,
      `${currentYear} report`
    ];

    return {
      currentDate,
      currentYear,
      currentMonth,
      timeContext: `Current date: ${currentDate}. Focus on the most recent and up-to-date information available.`,
      searchDateFilters
    };
  }

  /**
   * Enhanced search with temporal awareness
   */
  private enhanceQueriesWithTemporalContext(queries: string[], topic: string): string[] {
    const temporal = this.getCurrentTemporalContext();
    const enhancedQueries: string[] = [];
    
    // Add original queries
    enhancedQueries.push(...queries);
    
    // Add temporal-enhanced versions unless user specifies historical focus
    const isHistoricalTopic = topic.toLowerCase().includes('history') || 
                             topic.toLowerCase().includes('historical') ||
                             /\b(19|20)\d{2}\b/.test(topic); // Contains specific years
    
    if (!isHistoricalTopic) {
      // Add current year context to key queries
      const priorityQueries = queries.slice(0, 5);
      priorityQueries.forEach(query => {
        enhancedQueries.push(`${query} ${temporal.currentYear}`);
        enhancedQueries.push(`${query} latest ${temporal.currentYear}`);
        enhancedQueries.push(`${query} current trends ${temporal.currentYear}`);
      });
      
      // Add specific temporal research queries with current information focus
      enhancedQueries.push(
        `${topic} ${temporal.currentYear} latest updates current features`,
        `${topic} ${temporal.currentYear} pricing plans subscription costs`,
        `${topic} ${temporal.currentYear} product name changes rebrandings`,
        `${topic} ${temporal.currentYear} AI models technology used`,
        `${topic} ${temporal.currentYear} feature comparison table`,
        `${topic} ${temporal.currentYear} official website current pricing`,
        `${topic} recent updates ${temporal.currentMonth} ${temporal.currentYear}`,
        `${topic} ${temporal.currentYear} vs competitors comparison`,
        `${topic} ${temporal.currentYear} specifications technical details`,
        `${topic} ${temporal.currentYear} official announcements news`
      );
      
      // Add specific queries for alternatives/comparison topics
      if (topic.toLowerCase().includes('alternative') || topic.toLowerCase().includes('vs') || topic.toLowerCase().includes('comparison')) {
        enhancedQueries.push(
          `${topic.replace(/alternatives?/i, '').trim()} competitors ${temporal.currentYear} current pricing`,
          `${topic.replace(/alternatives?/i, '').trim()} similar tools ${temporal.currentYear} feature comparison`,
          `${topic.replace(/alternatives?/i, '').trim()} vs competitors ${temporal.currentYear} detailed comparison`,
          `best ${topic.replace(/alternatives?/i, '').trim()} competitors ${temporal.currentYear} pricing models`
        );
      }
    }
    
    return enhancedQueries;
  }

  async execute(state: AgentState): Promise<AgentState> {
    const startTime = Date.now();
    const temporal = this.getCurrentTemporalContext();

    this.log(state, `🔬 Enhanced Research Agent: Starting deep research with multi-step analysis (${temporal.currentDate})`);
    this.log(state, `📅 Temporal Context: Prioritizing ${temporal.currentYear} information with iterative refinement`);

    try {
      // Update state to research phase
      state.currentPhase = AgentPhase.RESEARCH;

      // PHASE 1: Deep Research Planning
      this.researchPlan = await this.createDeepResearchPlan(state);
      this.log(state, `📋 Deep research plan created with ${this.researchPlan.phases.length} phases`);

      // PHASE 2: Multi-Step Iterative Research
      const researchResults = await this.executeMultiStepResearch(state);
      this.log(state, `🔍 Multi-step research completed: ${researchResults.totalSources} sources analyzed`);

      // PHASE 3: Knowledge Graph Construction
      if (this.config.knowledgeGraphEnabled) {
        await this.buildKnowledgeGraph(state, researchResults);
        this.log(state, `🧠 Knowledge graph constructed with ${this.knowledgeGraph.size} entities`);
      }

      // PHASE 4: Deep Analysis and Synthesis
      const synthesizedFindings = await this.synthesizeResearchFindings(state, researchResults);
      state.researchData = synthesizedFindings.data;
      state.researchInsights = synthesizedFindings.insights;
      this.log(state, `📊 Research synthesis complete: ${synthesizedFindings.insights.length} key insights identified`);

      // PHASE 5: Quality Validation and Gap Analysis
      const validationResults = await this.validateAndIdentifyGaps(state);
      state.researchQuality = validationResults;
      this.log(state, `✅ Research validation complete: ${validationResults.qualityScore}% quality score`);

      // Mark research phase as complete
      state.completedPhases.push(AgentPhase.RESEARCH);
      state.currentPhase = AgentPhase.COMPETITION_ANALYSIS;

      const executionTime = Date.now() - startTime;
      this.log(state, `🎯 Research Agent completed in ${executionTime}ms with temporal enhancement`);

      return state;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown research error';
      state.errors.push({
        phase: 'research',
        error: errorMessage,
        timestamp: Date.now()
      });
      
      this.log(state, `❌ Research Agent failed: ${errorMessage}`);
      state.currentPhase = AgentPhase.FAILED;
      
      return state;
    }
  }

  private async performTemporallyAwarePrimarySearch(state: AgentState): Promise<Array<{url: string, content: string, title: string}>> {
    const temporal = this.getCurrentTemporalContext();
    this.log(state, `🔍 Searching for "${state.topic}" with ${temporal.currentYear} priority - targeting ${this.config.maxUrls} URLs`);
    
    try {
      // Enhanced search with temporal context
      const temporalSearchQuery = this.shouldPrioritizeCurrentInfo(state.topic) 
        ? `${state.topic} ${temporal.currentYear} latest`
        : state.topic;
      
      // Search for the topic with temporal awareness
      const searchResults = await this.searchService.search(temporalSearchQuery, this.config.maxUrls);

      // Extract top URLs (use url property from Tavily API)
      const topUrls = searchResults.items.slice(0, this.config.maxUrls)
        .map((result: any) => result.url || result.link) // Support both url and link properties
        .filter(url => url); // Filter out undefined URLs
      this.log(state, `📋 Selected ${topUrls.length} URLs for temporal-aware scraping`);

      // Scrape URLs in parallel batches
      const batchSize = Math.min(this.config.parallelSearches, topUrls.length);
      const scrapedData: Array<{url: string, content: string, title: string}> = [];

      for (let i = 0; i < topUrls.length; i += batchSize) {
        const batch = topUrls.slice(i, i + batchSize);
        const batchPromises = batch.map(async (url) => {
          try {
            const scraped = await this.webScraperService.scrapeUrl(url);
            return {
              url,
              content: scraped.content || '',
              title: scraped.title || `Content from ${url}`
            };
          } catch (error) {
            this.log(state, `⚠️ Failed to scrape ${url}: ${error}`);
            return null;
          }
        });

        const batchResults = await Promise.all(batchPromises);
        scrapedData.push(...batchResults.filter(result => result !== null) as Array<{url: string, content: string, title: string}>);
        
        this.log(state, `📄 Scraped batch ${Math.floor(i/batchSize) + 1}: ${batchResults.filter(r => r).length}/${batch.length} successful`);
      }

      // Filter and prioritize by temporal relevance
      const temporallyFilteredData = this.prioritizeByTemporalRelevance(scrapedData, temporal);
      return temporallyFilteredData.filter(data => data.content.length > 500); // Filter out thin content
      
    } catch (error) {
      this.log(state, `❌ Primary search failed: ${error}`);
      throw error;
    }
  }

  private async generateTemporallyAwareResearchQueries(state: AgentState, primaryUrls: Array<{url: string, content: string, title: string}>): Promise<string[]> {
    const temporal = this.getCurrentTemporalContext();
    this.log(state, `🧠 Analyzing content to generate temporally-aware research queries (${temporal.currentDate})`);

    const contentSample = primaryUrls.slice(0, 5).map(url => ({
      title: url.title,
      content: url.content.substring(0, 2000) // First 2000 chars
    }));

    const analysisPrompt = `You are an expert research strategist analyzing content about "${state.topic}" with a focus on gathering the most current and up-to-date information available.

📅 **TEMPORAL CONTEXT:**
- Current Date: ${temporal.currentDate}
- Research Priority: Latest ${temporal.currentYear} information unless topic requires historical focus
- Focus: Most recent developments, current statistics, and up-to-date insights

**Primary Content Analysis:**
${JSON.stringify(contentSample, null, 2)}

**Custom Instructions:**
${state.customInstructions || 'None'}

**Target Audience:** ${state.targetAudience || 'General audience'}
**Content Length:** ${state.contentLength || 2000} words
**Tone:** ${state.tone || 'Professional'}

Generate ${this.config.researchQueries} specific, temporally-aware research queries that prioritize finding ACTUAL INFORMATION AND DETAILS. Focus on:

1. **Latest Developments (${temporal.currentYear})**: What are the most recent changes, updates, and innovations?
2. **Current Statistics**: What are the latest figures, percentages, and data points for ${temporal.currentYear}?
3. **Recent Trends**: What patterns and movements have emerged in ${temporal.currentYear}?
4. **Up-to-date Practices**: What are the current best practices and methodologies as of ${temporal.currentYear}?
5. **Fresh Expert Insights**: What are thought leaders saying about this topic in ${temporal.currentYear}?
6. **Current Market Conditions**: How has the landscape changed recently?
7. **Recent Case Studies**: What new examples and applications have emerged?
8. **Latest Research**: What new studies and findings have been published recently?

**CRITICAL RESEARCH FOCUS:**
• Find CURRENT and UP-TO-DATE information from 2024-2025 - avoid outdated sources
• Look for LATEST pricing, current product names, recent rebrandings, and newest features
• Verify product names haven't changed (e.g., "Codeium" → "Windsurf", "Replit Ghostwriter" → "Replit")
• Find ACTUAL pricing tiers, subscription costs, and current feature sets
• Gather SPECIFIC technical details: AI models used, capabilities, performance metrics
• Look for RECENT announcements, updates, and current product positioning
• Prioritize official sources, recent press releases, and current documentation
• Each query should find the MOST CURRENT version of information, not historical data

Return ONLY a JSON array of research queries focused on current information:
["query1", "query2", "query3", ...]`;

    try {
      const response = await this.geminiService.generateContent(
        analysisPrompt,
        { temperature: 0.7, maxOutputTokens: 2048 },
        'Temporally-Aware Research Query Generation'
      );

      const queries = this.parseJsonResponse(response.response);
      const baseQueries = Array.isArray(queries) ? queries.slice(0, this.config.researchQueries) : [];
      
      // Enhance with temporal context
      const enhancedQueries = this.enhanceQueriesWithTemporalContext(baseQueries, state.topic);
      
      return enhancedQueries.slice(0, this.config.researchQueries + 5); // Allow few extra for temporal variants
      
    } catch (error) {
      this.log(state, `⚠️ Query generation failed, using temporally-aware fallback queries: ${error}`);
      
      // Fallback queries with temporal awareness
      const fallbackQueries = [
        `${state.topic} ${temporal.currentYear} latest developments`,
        `${state.topic} ${temporal.currentYear} current trends`,
        `${state.topic} ${temporal.currentYear} recent statistics`,
        `${state.topic} ${temporal.currentYear} best practices`,
        `${state.topic} latest news ${temporal.currentYear}`,
        `${state.topic} current market analysis ${temporal.currentYear}`,
        `${state.topic} recent updates ${temporal.currentMonth}`,
        `${state.topic} ${temporal.currentYear} industry report`
      ];
      
      return fallbackQueries.slice(0, this.config.researchQueries);
    }
  }

  private async executeTemporallyEnhancedResearch(state: AgentState, queries: string[]): Promise<Array<{query: string, results: any[], source: string}>> {
    const temporal = this.getCurrentTemporalContext();
    this.log(state, `🔎 Executing ${queries.length} temporally-enhanced research queries with ${temporal.currentYear} priority`);
    
    const researchData: Array<{query: string, results: any[], source: string}> = [];
    const batchSize = this.config.parallelSearches;

    for (let i = 0; i < queries.length; i += batchSize) {
      const batch = queries.slice(i, i + batchSize);
      
      const batchPromises = batch.map(async (query) => {
        try {
          // Enhanced search with temporal awareness
          const searchResults = await this.searchService.search(query, 7);

          return {
            query,
            results: this.filterTemporallyRelevantResults(searchResults.items, temporal),
            source: 'tavily_temporal_search'
          };
        } catch (error) {
          this.log(state, `⚠️ Research query failed: ${query} - ${error}`);
          return {
            query,
            results: [],
            source: 'failed'
          };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      researchData.push(...batchResults);
      
      this.log(state, `📊 Research batch ${Math.floor(i/batchSize) + 1} complete: ${batchResults.length} temporally-enhanced queries processed`);
    }

    return researchData.filter(data => data.results.length > 0);
  }

  private async analyzeTemporalResearchFindings(state: AgentState): Promise<void> {
    const temporal = this.getCurrentTemporalContext();
    this.log(state, `📈 Analyzing research findings with temporal validation (${temporal.currentDate})`);

    if (!state.researchData || !state.primaryUrls) {
      throw new Error('Research data or primary URLs missing');
    }

    // Calculate research statistics with temporal awareness
    const totalDataPoints = state.researchData.reduce((sum, data) => sum + data.results.length, 0);
    const uniqueDomains = new Set();
    let currentYearSources = 0;
    
    state.researchData.forEach(data => {
      data.results.forEach(result => {
        try {
          const domain = new URL(result.url).hostname;
          uniqueDomains.add(domain);
          
          // Count sources with current year relevance
          if (this.hasCurrentYearRelevance(result, temporal)) {
            currentYearSources++;
          }
        } catch (e) {
          // Invalid URL, skip
        }
      });
    });

    this.log(state, `📊 Temporal Research Summary: ${totalDataPoints} data points from ${uniqueDomains.size} unique domains`);
    this.log(state, `📅 Current Year Relevance: ${currentYearSources}/${totalDataPoints} sources contain ${temporal.currentYear} information`);

    // Store temporal research metadata in state
    state.logs.push({
      agent: this.agentId,
      message: `Temporal research complete: ${state.primaryUrls.length} primary sources, ${state.researchData.length} research queries, ${totalDataPoints} data points, ${currentYearSources} current-year sources`,
      timestamp: Date.now()
    });
  }

  /**
   * Helper methods for temporal awareness
   */
  private shouldPrioritizeCurrentInfo(topic: string): boolean {
    const historicalIndicators = ['history', 'historical', 'past', 'ancient', 'medieval', 'classical'];
    const specificYearPattern = /\b(19|20)\d{2}\b/;
    
    const topicLower = topic.toLowerCase();
    const hasHistoricalIndicator = historicalIndicators.some(indicator => topicLower.includes(indicator));
    const hasSpecificYear = specificYearPattern.test(topic);
    
    return !hasHistoricalIndicator && !hasSpecificYear;
  }

  private prioritizeByTemporalRelevance(data: Array<{url: string, content: string, title: string}>, temporal: any): Array<{url: string, content: string, title: string}> {
    return data.sort((a, b) => {
      const aRelevance = this.calculateTemporalRelevance(a, temporal);
      const bRelevance = this.calculateTemporalRelevance(b, temporal);
      return bRelevance - aRelevance; // Higher relevance first
    });
  }

  private calculateTemporalRelevance(item: {url: string, content: string, title: string}, temporal: any): number {
    let score = 0;
    const content = (item.title + ' ' + item.content).toLowerCase();
    
    // Check for current year mentions
    if (content.includes(temporal.currentYear.toString())) score += 10;
    if (content.includes(temporal.currentMonth.toLowerCase())) score += 8;
    
    // Check for recency indicators
    const recencyTerms = ['latest', 'recent', 'current', 'new', 'updated', '2025'];
    recencyTerms.forEach(term => {
      if (content.includes(term)) score += 5;
    });
    
    return score;
  }

  private filterTemporallyRelevantResults(results: any[], temporal: any): any[] {
    return results.map(result => ({
      ...result,
      temporalRelevance: this.calculateTemporalRelevance(result, temporal)
    })).sort((a, b) => b.temporalRelevance - a.temporalRelevance);
  }

  private hasCurrentYearRelevance(result: any, temporal: any): boolean {
    const content = (result.title + ' ' + (result.content || result.snippet || '')).toLowerCase();
    return content.includes(temporal.currentYear.toString()) || 
           content.includes('2025') || 
           content.includes('latest') || 
           content.includes('current');
  }

  private parseJsonResponse(response: string): any {
    try {
      // Remove markdown code blocks if present
      let cleanResponse = response.trim();
      
      // Remove ```json and ``` markers
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }
      
      // Try to parse the cleaned response
      return JSON.parse(cleanResponse.trim());
    } catch (error) {
      // If JSON parsing fails, return a fallback structure
      console.warn('Failed to parse JSON response, using fallback:', error);
      return [];
    }
  }

  private log(state: AgentState, message: string): void {
    const logEntry = {
      agent: this.agentId,
      message,
      timestamp: Date.now()
    };
    
    state.logs.push(logEntry);
    console.log(`[${this.agentId}] ${message}`);
  }

  /**
   * Create a comprehensive deep research plan
   */
  private async createDeepResearchPlan(state: AgentState): Promise<any> {
    this.log(state, '📋 Creating deep research plan with multi-phase approach');

    const prompt = `
DEEP RESEARCH PLANNING SYSTEM - 2025 METHODOLOGY

Create a comprehensive research plan for the topic: "${state.topic}"

RESEARCH OBJECTIVES:
- Target Audience: ${state.targetAudience || 'General audience'}
- Content Length: ${state.contentLength || 2000} words
- Tone: ${state.tone || 'Professional'}
- Custom Instructions: ${state.customInstructions || 'None'}

DEEP RESEARCH METHODOLOGY:
1. EXPLORATORY PHASE - Broad topic understanding
2. FOCUSED PHASE - Specific aspects deep dive
3. VALIDATION PHASE - Fact checking and verification
4. SYNTHESIS PHASE - Knowledge integration
5. GAP ANALYSIS PHASE - Identify missing information

For each phase, provide:
- Specific search queries (3-5 per phase)
- Expected information types
- Quality criteria
- Success metrics

Return JSON format:
{
  "phases": [
    {
      "name": "exploratory",
      "description": "Broad understanding of the topic",
      "queries": ["query1", "query2", "query3"],
      "expectedInfo": ["info_type1", "info_type2"],
      "qualityCriteria": ["criteria1", "criteria2"],
      "successMetrics": ["metric1", "metric2"]
    }
  ],
  "totalEstimatedSources": 50,
  "estimatedDuration": "15-20 minutes",
  "qualityThreshold": 85
}`;

    try {
      const response = await this.geminiService.generateContent(prompt);
      const plan = this.parseJsonResponse(response.response);

      // Add default plan if parsing fails
      if (!plan.phases) {
        return this.getDefaultResearchPlan(state.topic);
      }

      return plan;
    } catch (error) {
      this.log(state, `⚠️ Research plan generation failed, using default: ${error}`);
      return this.getDefaultResearchPlan(state.topic);
    }
  }

  /**
   * Execute multi-step iterative research
   */
  private async executeMultiStepResearch(state: AgentState): Promise<any> {
    this.log(state, '🔍 Starting multi-step iterative research process');

    const allResults: any[] = [];
    let totalSources = 0;

    for (const phase of this.researchPlan.phases) {
      this.log(state, `📊 Executing ${phase.name} phase with ${phase.queries.length} queries`);

      // Execute phase queries
      const phaseResults = await this.executePhaseQueries(state, phase);
      allResults.push(...phaseResults);
      totalSources += phaseResults.length;

      // Analyze phase results and refine next queries
      if (this.config.iterativeRefinement) {
        const refinements = await this.analyzeAndRefineQueries(state, phaseResults, phase);
        if (refinements.length > 0) {
          this.log(state, `🔄 Executing ${refinements.length} refined queries for ${phase.name}`);
          const refinedResults = await this.executeRefinedQueries(state, refinements);
          allResults.push(...refinedResults);
          totalSources += refinedResults.length;
        }
      }
    }

    return {
      results: allResults,
      totalSources,
      phaseBreakdown: this.researchPlan.phases.map((phase: any) => ({
        name: phase.name,
        queriesExecuted: phase.queries.length,
        sourcesFound: allResults.filter((r: any) => r.phase === phase.name).length
      }))
    };
  }

  /**
   * Build knowledge graph from research results
   */
  private async buildKnowledgeGraph(state: AgentState, researchResults: any): Promise<void> {
    this.log(state, '🧠 Building knowledge graph from research findings');

    // Extract entities and relationships
    const entities = await this.extractEntities(researchResults.results);
    const relationships = await this.identifyRelationships(entities);

    // Build graph structure
    entities.forEach((entity: any) => {
      this.knowledgeGraph.set(entity.id, {
        ...entity,
        connections: relationships.filter((rel: any) =>
          rel.source === entity.id || rel.target === entity.id
        )
      });
    });

    this.log(state, `✅ Knowledge graph built: ${entities.length} entities, ${relationships.length} relationships`);
  }

  /**
   * Synthesize research findings into actionable insights
   */
  private async synthesizeResearchFindings(state: AgentState, researchResults: any): Promise<any> {
    this.log(state, '📊 Synthesizing research findings into actionable insights');

    const prompt = `
RESEARCH SYNTHESIS AND INSIGHT GENERATION

Analyze the following research data for topic "${state.topic}" and generate key insights:

RESEARCH DATA:
${JSON.stringify(researchResults.results.slice(0, 10), null, 2)}

SYNTHESIS REQUIREMENTS:
1. Identify 5-10 key insights with confidence scores
2. Categorize insights by importance and relevance
3. Provide supporting evidence for each insight
4. Identify patterns and trends
5. Highlight contradictions or gaps

Return JSON format:
{
  "insights": [
    {
      "insight": "Key finding or pattern",
      "confidence": 0.95,
      "sources": ["url1", "url2"],
      "category": "trend|fact|opinion|statistic",
      "importance": "high|medium|low",
      "evidence": "Supporting evidence"
    }
  ],
  "patterns": ["pattern1", "pattern2"],
  "contradictions": ["contradiction1"],
  "dataQuality": {
    "reliability": 0.85,
    "completeness": 0.90,
    "recency": 0.95
  }
}`;

    try {
      const response = await this.geminiService.generateContent(prompt);
      const synthesis = this.parseJsonResponse(response.response);

      return {
        data: researchResults.results,
        insights: synthesis.insights || [],
        patterns: synthesis.patterns || [],
        contradictions: synthesis.contradictions || [],
        quality: synthesis.dataQuality || { reliability: 0.7, completeness: 0.7, recency: 0.8 }
      };
    } catch (error) {
      this.log(state, `⚠️ Research synthesis failed: ${error}`);
      return {
        data: researchResults.results,
        insights: [],
        patterns: [],
        contradictions: [],
        quality: { reliability: 0.5, completeness: 0.5, recency: 0.5 }
      };
    }
  }

  /**
   * Validate research quality and identify gaps
   */
  private async validateAndIdentifyGaps(state: AgentState): Promise<any> {
    this.log(state, '✅ Validating research quality and identifying gaps');

    const researchData = state.researchData || [];
    const insights = state.researchInsights || [];

    // Calculate quality metrics
    const qualityScore = Math.min(100, (researchData.length * 5) + (insights.length * 10));
    const completeness = Math.min(100, researchData.length * 8);
    const reliability = insights.reduce((acc, insight) => acc + insight.confidence, 0) / Math.max(insights.length, 1) * 100;

    // Identify gaps
    const gaps = [];
    if (researchData.length < 10) gaps.push('Insufficient data sources');
    if (insights.length < 5) gaps.push('Limited insights generated');
    if (reliability < 70) gaps.push('Low confidence in findings');

    return {
      qualityScore: Math.round(qualityScore),
      completeness: Math.round(completeness),
      reliability: Math.round(reliability),
      gaps,
      recommendations: gaps.length > 0 ? ['Conduct additional research', 'Verify sources', 'Seek expert opinions'] : ['Research quality is satisfactory']
    };
  }

  /**
   * Default research plan fallback
   */
  private getDefaultResearchPlan(topic: string): any {
    return {
      phases: [
        {
          name: 'exploratory',
          description: 'Broad understanding of the topic',
          queries: [`${topic} overview 2025`, `${topic} latest trends`, `${topic} current state`],
          expectedInfo: ['general_info', 'trends', 'statistics'],
          qualityCriteria: ['recency', 'authority', 'comprehensiveness'],
          successMetrics: ['source_count', 'information_depth']
        },
        {
          name: 'focused',
          description: 'Deep dive into specific aspects',
          queries: [`${topic} detailed analysis`, `${topic} expert opinions`, `${topic} case studies`],
          expectedInfo: ['detailed_analysis', 'expert_insights', 'examples'],
          qualityCriteria: ['depth', 'expertise', 'evidence'],
          successMetrics: ['insight_quality', 'expert_sources']
        },
        {
          name: 'validation',
          description: 'Fact checking and verification',
          queries: [`${topic} research studies`, `${topic} official data`, `${topic} peer reviewed`],
          expectedInfo: ['research_data', 'official_sources', 'peer_review'],
          qualityCriteria: ['credibility', 'peer_review', 'official_status'],
          successMetrics: ['source_credibility', 'data_verification']
        }
      ],
      totalEstimatedSources: 30,
      estimatedDuration: '10-15 minutes',
      qualityThreshold: 75
    };
  }

  /**
   * Execute queries for a specific research phase
   */
  private async executePhaseQueries(state: AgentState, phase: any): Promise<any[]> {
    const results = [];

    for (const query of phase.queries) {
      try {
        const searchResults = await this.searchService.search(query, 5, {
          searchDepth: 'deep',
          prioritizeRecent: true
        });

        results.push({
          phase: phase.name,
          query,
          results: searchResults.items,
          source: 'tavily_deep_search',
          timestamp: Date.now()
        });
      } catch (error) {
        this.log(state, `⚠️ Phase query failed: ${query} - ${error}`);
      }
    }

    return results;
  }

  /**
   * Analyze results and generate refined queries
   */
  private async analyzeAndRefineQueries(state: AgentState, phaseResults: any[], phase: any): Promise<string[]> {
    if (phaseResults.length === 0) return [];

    const prompt = `
Analyze these search results and generate 2-3 refined queries to fill information gaps:

PHASE: ${phase.name}
RESULTS SUMMARY: ${phaseResults.length} sources found
SAMPLE RESULTS: ${JSON.stringify(phaseResults.slice(0, 3), null, 2)}

Generate refined queries that:
1. Address information gaps
2. Seek more specific details
3. Target different perspectives

Return only a JSON array of strings: ["query1", "query2", "query3"]`;

    try {
      const response = await this.geminiService.generateContent(prompt);
      const refinedQueries = this.parseJsonResponse(response.response);
      return Array.isArray(refinedQueries) ? refinedQueries.slice(0, 3) : [];
    } catch (error) {
      return [];
    }
  }

  /**
   * Execute refined queries
   */
  private async executeRefinedQueries(state: AgentState, queries: string[]): Promise<any[]> {
    const results = [];

    for (const query of queries) {
      try {
        const searchResults = await this.searchService.search(query, 3, {
          searchDepth: 'advanced',
          prioritizeRecent: true
        });

        results.push({
          phase: 'refinement',
          query,
          results: searchResults.items,
          source: 'tavily_refined_search',
          timestamp: Date.now()
        });
      } catch (error) {
        this.log(state, `⚠️ Refined query failed: ${query} - ${error}`);
      }
    }

    return results;
  }

  /**
   * Extract entities from research results
   */
  private async extractEntities(results: any[]): Promise<any[]> {
    // Simplified entity extraction - in a real implementation, this would use NLP
    const entities = [];
    let entityId = 1;

    for (const result of results.slice(0, 10)) {
      if (result.results && result.results.length > 0) {
        for (const item of result.results.slice(0, 2)) {
          entities.push({
            id: `entity_${entityId++}`,
            type: 'concept',
            name: item.title || 'Unknown',
            source: item.url || 'Unknown',
            relevance: 0.8
          });
        }
      }
    }

    return entities;
  }

  /**
   * Identify relationships between entities
   */
  private async identifyRelationships(entities: any[]): Promise<any[]> {
    // Simplified relationship identification
    const relationships = [];

    for (let i = 0; i < entities.length - 1; i++) {
      for (let j = i + 1; j < entities.length; j++) {
        if (Math.random() > 0.7) { // Simplified relationship detection
          relationships.push({
            source: entities[i].id,
            target: entities[j].id,
            type: 'related_to',
            strength: Math.random() * 0.5 + 0.5
          });
        }
      }
    }

    return relationships;
  }

  getCapabilities() {
    return {
      name: 'Enhanced Deep Research Agent',
      description: 'Advanced multi-step research with iterative refinement and knowledge graph construction',
      inputTypes: ['topic', 'customInstructions', 'targetAudience'],
      outputTypes: ['primaryUrls', 'researchQueries', 'researchData', 'researchInsights', 'researchQuality'],
      dependencies: ['search-service', 'web-scraper', 'gemini-service'],
      parallel: true,
      features: ['deep-research', 'iterative-refinement', 'knowledge-graph', 'multi-step-analysis']
    };
  }

  getMetrics(state: AgentState) {
    const researchLogs = state.logs.filter(log => log.agent === this.agentId);
    return {
      executionTime: researchLogs.length > 0 ? Date.now() - researchLogs[0].timestamp : 0,
      successRate: state.errors.filter(e => e.phase === 'research').length === 0 ? 100 : 0,
      qualityScore: state.researchData ? Math.min(100, state.researchData.length * 8.33) : 0,
      retryCount: state.retryCount,
      errorCount: state.errors.filter(e => e.phase === 'research').length,
      lastExecution: Date.now()
    };
  }
} 