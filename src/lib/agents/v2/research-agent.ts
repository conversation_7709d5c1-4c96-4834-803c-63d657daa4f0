/**
 * Research Agent for Invincible v.2 Multi-Agent System
 * Specialized agent responsible for comprehensive research and data gathering
 * Enhanced with temporal awareness and latest fact gathering capabilities
 */

import { TavilySearchService } from '../../search';
import { NodeWebScraperService } from '../../web-scraper';
import { GeminiService } from '../../gemini';
import { KnowledgeBase } from '../../knowledge-base';
import { AgentState, AgentPhase, ResearchAgentConfig } from './types';

export class ResearchAgent {
  private searchService: TavilySearchService;
  private webScraperService: NodeWebScraperService;
  private geminiService: GeminiService;
  private config: ResearchAgentConfig;
  private agentId: string;
  private currentDate: Date;

  constructor(config: Partial<ResearchAgentConfig> = {}) {
    this.config = {
      searchDepth: config.searchDepth ?? 5, // Reduced from 10 for speed
      maxUrls: config.maxUrls ?? 8, // Reduced from 15 for speed  
      parallelSearches: config.parallelSearches ?? 2, // Reduced from 3 for speed
      researchQueries: config.researchQueries ?? 6 // Reduced from 12 for speed
    };
    
    this.agentId = 'research-agent';
    this.searchService = new TavilySearchService();
    this.webScraperService = new NodeWebScraperService();
    this.geminiService = new GeminiService();
    this.currentDate = new Date();
  }

  /**
   * Get current date and time for temporal context
   */
  private getCurrentTemporalContext(): {
    currentDate: string;
    currentYear: number;
    currentMonth: string;
    timeContext: string;
    searchDateFilters: string[];
  } {
    const now = this.currentDate;
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    
    const currentYear = now.getFullYear();
    const currentMonth = monthNames[now.getMonth()];
    const currentDate = `${currentMonth} ${currentYear}`;
    
    // Create time-based search filters for latest information
    const searchDateFilters = [
      `${currentYear}`,
      `${currentMonth} ${currentYear}`,
      `recent ${currentYear}`,
      `latest ${currentYear}`,
      `current ${currentYear}`,
      `updated ${currentYear}`,
      `new ${currentYear}`,
      `${currentYear} data`,
      `${currentYear} statistics`,
      `${currentYear} report`
    ];

    return {
      currentDate,
      currentYear,
      currentMonth,
      timeContext: `Current date: ${currentDate}. Focus on the most recent and up-to-date information available.`,
      searchDateFilters
    };
  }

  /**
   * Enhanced search with temporal awareness
   */
  private enhanceQueriesWithTemporalContext(queries: string[], topic: string): string[] {
    const temporal = this.getCurrentTemporalContext();
    const enhancedQueries: string[] = [];
    
    // Add original queries
    enhancedQueries.push(...queries);
    
    // Add temporal-enhanced versions unless user specifies historical focus
    const isHistoricalTopic = topic.toLowerCase().includes('history') || 
                             topic.toLowerCase().includes('historical') ||
                             /\b(19|20)\d{2}\b/.test(topic); // Contains specific years
    
    if (!isHistoricalTopic) {
      // Add current year context to key queries
      const priorityQueries = queries.slice(0, 5);
      priorityQueries.forEach(query => {
        enhancedQueries.push(`${query} ${temporal.currentYear}`);
        enhancedQueries.push(`${query} latest ${temporal.currentYear}`);
        enhancedQueries.push(`${query} current trends ${temporal.currentYear}`);
      });
      
      // Add specific temporal research queries with current information focus
      enhancedQueries.push(
        `${topic} ${temporal.currentYear} latest updates current features`,
        `${topic} ${temporal.currentYear} pricing plans subscription costs`,
        `${topic} ${temporal.currentYear} product name changes rebrandings`,
        `${topic} ${temporal.currentYear} AI models technology used`,
        `${topic} ${temporal.currentYear} feature comparison table`,
        `${topic} ${temporal.currentYear} official website current pricing`,
        `${topic} recent updates ${temporal.currentMonth} ${temporal.currentYear}`,
        `${topic} ${temporal.currentYear} vs competitors comparison`,
        `${topic} ${temporal.currentYear} specifications technical details`,
        `${topic} ${temporal.currentYear} official announcements news`
      );
      
      // Add specific queries for alternatives/comparison topics
      if (topic.toLowerCase().includes('alternative') || topic.toLowerCase().includes('vs') || topic.toLowerCase().includes('comparison')) {
        enhancedQueries.push(
          `${topic.replace(/alternatives?/i, '').trim()} competitors ${temporal.currentYear} current pricing`,
          `${topic.replace(/alternatives?/i, '').trim()} similar tools ${temporal.currentYear} feature comparison`,
          `${topic.replace(/alternatives?/i, '').trim()} vs competitors ${temporal.currentYear} detailed comparison`,
          `best ${topic.replace(/alternatives?/i, '').trim()} competitors ${temporal.currentYear} pricing models`
        );
      }
    }
    
    return enhancedQueries;
  }

  async execute(state: AgentState): Promise<AgentState> {
    const startTime = Date.now();
    const temporal = this.getCurrentTemporalContext();
    
    this.log(state, `🔬 Research Agent: Starting comprehensive research phase (${temporal.currentDate})`);
    this.log(state, `📅 Temporal Context: Prioritizing ${temporal.currentYear} information unless historical focus detected`);

    try {
      // Update state to research phase
      state.currentPhase = AgentPhase.RESEARCH;
      
      // Step 1: Primary search and URL extraction with temporal awareness
      const primaryUrls = await this.performTemporallyAwarePrimarySearch(state);
      state.primaryUrls = primaryUrls;
      this.log(state, `📊 Primary search complete: ${primaryUrls.length} URLs found with temporal relevance`);

      // Step 2: Generate intelligent research queries with temporal context
      const researchQueries = await this.generateTemporallyAwareResearchQueries(state, primaryUrls);
      state.researchQueries = researchQueries;
      this.log(state, `🧠 Generated ${researchQueries.length} temporally-aware research queries`);

      // Step 3: Execute comprehensive research with latest information priority
      const researchData = await this.executeTemporallyEnhancedResearch(state, researchQueries);
      state.researchData = researchData;
      this.log(state, `📚 Research complete: ${researchData.length} data sources gathered with ${temporal.currentYear} priority`);

      // Step 4: Analyze and structure research findings with temporal validation
      await this.analyzeTemporalResearchFindings(state);
      this.log(state, '✅ Research analysis and temporal validation complete');

      // Mark research phase as complete
      state.completedPhases.push(AgentPhase.RESEARCH);
      state.currentPhase = AgentPhase.COMPETITION_ANALYSIS;

      const executionTime = Date.now() - startTime;
      this.log(state, `🎯 Research Agent completed in ${executionTime}ms with temporal enhancement`);

      return state;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown research error';
      state.errors.push({
        phase: 'research',
        error: errorMessage,
        timestamp: Date.now()
      });
      
      this.log(state, `❌ Research Agent failed: ${errorMessage}`);
      state.currentPhase = AgentPhase.FAILED;
      
      return state;
    }
  }

  private async performTemporallyAwarePrimarySearch(state: AgentState): Promise<Array<{url: string, content: string, title: string}>> {
    const temporal = this.getCurrentTemporalContext();
    this.log(state, `🔍 Searching for "${state.topic}" with ${temporal.currentYear} priority - targeting ${this.config.maxUrls} URLs`);
    
    try {
      // Enhanced search with temporal context
      const temporalSearchQuery = this.shouldPrioritizeCurrentInfo(state.topic) 
        ? `${state.topic} ${temporal.currentYear} latest`
        : state.topic;
      
      // Search for the topic with temporal awareness
      const searchResults = await this.searchService.search(temporalSearchQuery, this.config.maxUrls);

      // Extract top URLs (use url property from Tavily API)
      const topUrls = searchResults.items.slice(0, this.config.maxUrls)
        .map((result: any) => result.url || result.link) // Support both url and link properties
        .filter(url => url); // Filter out undefined URLs
      this.log(state, `📋 Selected ${topUrls.length} URLs for temporal-aware scraping`);

      // Scrape URLs in parallel batches
      const batchSize = Math.min(this.config.parallelSearches, topUrls.length);
      const scrapedData: Array<{url: string, content: string, title: string}> = [];

      for (let i = 0; i < topUrls.length; i += batchSize) {
        const batch = topUrls.slice(i, i + batchSize);
        const batchPromises = batch.map(async (url) => {
          try {
            const scraped = await this.webScraperService.scrapeUrl(url);
            return {
              url,
              content: scraped.content || '',
              title: scraped.title || `Content from ${url}`
            };
          } catch (error) {
            this.log(state, `⚠️ Failed to scrape ${url}: ${error}`);
            return null;
          }
        });

        const batchResults = await Promise.all(batchPromises);
        scrapedData.push(...batchResults.filter(result => result !== null) as Array<{url: string, content: string, title: string}>);
        
        this.log(state, `📄 Scraped batch ${Math.floor(i/batchSize) + 1}: ${batchResults.filter(r => r).length}/${batch.length} successful`);
      }

      // Filter and prioritize by temporal relevance
      const temporallyFilteredData = this.prioritizeByTemporalRelevance(scrapedData, temporal);
      return temporallyFilteredData.filter(data => data.content.length > 500); // Filter out thin content
      
    } catch (error) {
      this.log(state, `❌ Primary search failed: ${error}`);
      throw error;
    }
  }

  private async generateTemporallyAwareResearchQueries(state: AgentState, primaryUrls: Array<{url: string, content: string, title: string}>): Promise<string[]> {
    const temporal = this.getCurrentTemporalContext();
    this.log(state, `🧠 Analyzing content to generate temporally-aware research queries (${temporal.currentDate})`);

    const contentSample = primaryUrls.slice(0, 5).map(url => ({
      title: url.title,
      content: url.content.substring(0, 2000) // First 2000 chars
    }));

    const analysisPrompt = `You are an expert research strategist analyzing content about "${state.topic}" with a focus on gathering the most current and up-to-date information available.

📅 **TEMPORAL CONTEXT:**
- Current Date: ${temporal.currentDate}
- Research Priority: Latest ${temporal.currentYear} information unless topic requires historical focus
- Focus: Most recent developments, current statistics, and up-to-date insights

**Primary Content Analysis:**
${JSON.stringify(contentSample, null, 2)}

**Custom Instructions:**
${state.customInstructions || 'None'}

**Target Audience:** ${state.targetAudience || 'General audience'}
**Content Length:** ${state.contentLength || 2000} words
**Tone:** ${state.tone || 'Professional'}

Generate ${this.config.researchQueries} specific, temporally-aware research queries that prioritize finding ACTUAL INFORMATION AND DETAILS. Focus on:

1. **Latest Developments (${temporal.currentYear})**: What are the most recent changes, updates, and innovations?
2. **Current Statistics**: What are the latest figures, percentages, and data points for ${temporal.currentYear}?
3. **Recent Trends**: What patterns and movements have emerged in ${temporal.currentYear}?
4. **Up-to-date Practices**: What are the current best practices and methodologies as of ${temporal.currentYear}?
5. **Fresh Expert Insights**: What are thought leaders saying about this topic in ${temporal.currentYear}?
6. **Current Market Conditions**: How has the landscape changed recently?
7. **Recent Case Studies**: What new examples and applications have emerged?
8. **Latest Research**: What new studies and findings have been published recently?

**CRITICAL RESEARCH FOCUS:**
• Find CURRENT and UP-TO-DATE information from 2024-2025 - avoid outdated sources
• Look for LATEST pricing, current product names, recent rebrandings, and newest features
• Verify product names haven't changed (e.g., "Codeium" → "Windsurf", "Replit Ghostwriter" → "Replit")
• Find ACTUAL pricing tiers, subscription costs, and current feature sets
• Gather SPECIFIC technical details: AI models used, capabilities, performance metrics
• Look for RECENT announcements, updates, and current product positioning
• Prioritize official sources, recent press releases, and current documentation
• Each query should find the MOST CURRENT version of information, not historical data

Return ONLY a JSON array of research queries focused on current information:
["query1", "query2", "query3", ...]`;

    try {
      const response = await this.geminiService.generateContent(
        analysisPrompt,
        { temperature: 0.7, maxOutputTokens: 2048 },
        'Temporally-Aware Research Query Generation'
      );

      const queries = this.parseJsonResponse(response.response);
      const baseQueries = Array.isArray(queries) ? queries.slice(0, this.config.researchQueries) : [];
      
      // Enhance with temporal context
      const enhancedQueries = this.enhanceQueriesWithTemporalContext(baseQueries, state.topic);
      
      return enhancedQueries.slice(0, this.config.researchQueries + 5); // Allow few extra for temporal variants
      
    } catch (error) {
      this.log(state, `⚠️ Query generation failed, using temporally-aware fallback queries: ${error}`);
      
      // Fallback queries with temporal awareness
      const fallbackQueries = [
        `${state.topic} ${temporal.currentYear} latest developments`,
        `${state.topic} ${temporal.currentYear} current trends`,
        `${state.topic} ${temporal.currentYear} recent statistics`,
        `${state.topic} ${temporal.currentYear} best practices`,
        `${state.topic} latest news ${temporal.currentYear}`,
        `${state.topic} current market analysis ${temporal.currentYear}`,
        `${state.topic} recent updates ${temporal.currentMonth}`,
        `${state.topic} ${temporal.currentYear} industry report`
      ];
      
      return fallbackQueries.slice(0, this.config.researchQueries);
    }
  }

  private async executeTemporallyEnhancedResearch(state: AgentState, queries: string[]): Promise<Array<{query: string, results: any[], source: string}>> {
    const temporal = this.getCurrentTemporalContext();
    this.log(state, `🔎 Executing ${queries.length} temporally-enhanced research queries with ${temporal.currentYear} priority`);
    
    const researchData: Array<{query: string, results: any[], source: string}> = [];
    const batchSize = this.config.parallelSearches;

    for (let i = 0; i < queries.length; i += batchSize) {
      const batch = queries.slice(i, i + batchSize);
      
      const batchPromises = batch.map(async (query) => {
        try {
          // Enhanced search with temporal awareness
          const searchResults = await this.searchService.search(query, 7);

          return {
            query,
            results: this.filterTemporallyRelevantResults(searchResults.items, temporal),
            source: 'tavily_temporal_search'
          };
        } catch (error) {
          this.log(state, `⚠️ Research query failed: ${query} - ${error}`);
          return {
            query,
            results: [],
            source: 'failed'
          };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      researchData.push(...batchResults);
      
      this.log(state, `📊 Research batch ${Math.floor(i/batchSize) + 1} complete: ${batchResults.length} temporally-enhanced queries processed`);
    }

    return researchData.filter(data => data.results.length > 0);
  }

  private async analyzeTemporalResearchFindings(state: AgentState): Promise<void> {
    const temporal = this.getCurrentTemporalContext();
    this.log(state, `📈 Analyzing research findings with temporal validation (${temporal.currentDate})`);

    if (!state.researchData || !state.primaryUrls) {
      throw new Error('Research data or primary URLs missing');
    }

    // Calculate research statistics with temporal awareness
    const totalDataPoints = state.researchData.reduce((sum, data) => sum + data.results.length, 0);
    const uniqueDomains = new Set();
    let currentYearSources = 0;
    
    state.researchData.forEach(data => {
      data.results.forEach(result => {
        try {
          const domain = new URL(result.url).hostname;
          uniqueDomains.add(domain);
          
          // Count sources with current year relevance
          if (this.hasCurrentYearRelevance(result, temporal)) {
            currentYearSources++;
          }
        } catch (e) {
          // Invalid URL, skip
        }
      });
    });

    this.log(state, `📊 Temporal Research Summary: ${totalDataPoints} data points from ${uniqueDomains.size} unique domains`);
    this.log(state, `📅 Current Year Relevance: ${currentYearSources}/${totalDataPoints} sources contain ${temporal.currentYear} information`);

    // Store temporal research metadata in state
    state.logs.push({
      agent: this.agentId,
      message: `Temporal research complete: ${state.primaryUrls.length} primary sources, ${state.researchData.length} research queries, ${totalDataPoints} data points, ${currentYearSources} current-year sources`,
      timestamp: Date.now()
    });
  }

  /**
   * Helper methods for temporal awareness
   */
  private shouldPrioritizeCurrentInfo(topic: string): boolean {
    const historicalIndicators = ['history', 'historical', 'past', 'ancient', 'medieval', 'classical'];
    const specificYearPattern = /\b(19|20)\d{2}\b/;
    
    const topicLower = topic.toLowerCase();
    const hasHistoricalIndicator = historicalIndicators.some(indicator => topicLower.includes(indicator));
    const hasSpecificYear = specificYearPattern.test(topic);
    
    return !hasHistoricalIndicator && !hasSpecificYear;
  }

  private prioritizeByTemporalRelevance(data: Array<{url: string, content: string, title: string}>, temporal: any): Array<{url: string, content: string, title: string}> {
    return data.sort((a, b) => {
      const aRelevance = this.calculateTemporalRelevance(a, temporal);
      const bRelevance = this.calculateTemporalRelevance(b, temporal);
      return bRelevance - aRelevance; // Higher relevance first
    });
  }

  private calculateTemporalRelevance(item: {url: string, content: string, title: string}, temporal: any): number {
    let score = 0;
    const content = (item.title + ' ' + item.content).toLowerCase();
    
    // Check for current year mentions
    if (content.includes(temporal.currentYear.toString())) score += 10;
    if (content.includes(temporal.currentMonth.toLowerCase())) score += 8;
    
    // Check for recency indicators
    const recencyTerms = ['latest', 'recent', 'current', 'new', 'updated', '2025'];
    recencyTerms.forEach(term => {
      if (content.includes(term)) score += 5;
    });
    
    return score;
  }

  private filterTemporallyRelevantResults(results: any[], temporal: any): any[] {
    return results.map(result => ({
      ...result,
      temporalRelevance: this.calculateTemporalRelevance(result, temporal)
    })).sort((a, b) => b.temporalRelevance - a.temporalRelevance);
  }

  private hasCurrentYearRelevance(result: any, temporal: any): boolean {
    const content = (result.title + ' ' + (result.content || result.snippet || '')).toLowerCase();
    return content.includes(temporal.currentYear.toString()) || 
           content.includes('2025') || 
           content.includes('latest') || 
           content.includes('current');
  }

  private parseJsonResponse(response: string): any {
    try {
      // Remove markdown code blocks if present
      let cleanResponse = response.trim();
      
      // Remove ```json and ``` markers
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }
      
      // Try to parse the cleaned response
      return JSON.parse(cleanResponse.trim());
    } catch (error) {
      // If JSON parsing fails, return a fallback structure
      console.warn('Failed to parse JSON response, using fallback:', error);
      return [];
    }
  }

  private log(state: AgentState, message: string): void {
    const logEntry = {
      agent: this.agentId,
      message,
      timestamp: Date.now()
    };
    
    state.logs.push(logEntry);
    console.log(`[${this.agentId}] ${message}`);
  }

  getCapabilities() {
    return {
      name: 'Research Agent',
      description: 'Comprehensive research and data gathering specialist',
      inputTypes: ['topic', 'customInstructions', 'targetAudience'],
      outputTypes: ['primaryUrls', 'researchQueries', 'researchData'],
      dependencies: ['search-service', 'web-scraper', 'gemini-service'],
      parallel: true
    };
  }

  getMetrics(state: AgentState) {
    const researchLogs = state.logs.filter(log => log.agent === this.agentId);
    return {
      executionTime: researchLogs.length > 0 ? Date.now() - researchLogs[0].timestamp : 0,
      successRate: state.errors.filter(e => e.phase === 'research').length === 0 ? 100 : 0,
      qualityScore: state.researchData ? Math.min(100, state.researchData.length * 8.33) : 0,
      retryCount: state.retryCount,
      errorCount: state.errors.filter(e => e.phase === 'research').length,
      lastExecution: Date.now()
    };
  }
} 