/**
 * Invincible v.2 Multi-Agent System Types
 * Comprehensive type definitions for autonomous multi-agent architecture
 */

export interface AgentState {
  // Shared conversation state
  topic: string;
  customInstructions?: string;
  targetAudience?: string;
  contentLength?: number;
  tone?: string;
  keywords?: string[];
  contentType?: string;
  
  // Research phase state
  primaryUrls?: Array<{url: string, content: string, title: string}>;
  researchQueries?: string[];
  researchData?: Array<{query: string, results: any[], source: string}>;
  researchInsights?: Array<{
    insight: string;
    confidence: number;
    sources: string[];
    category: string;
  }>;
  researchQuality?: {
    qualityScore: number;
    completeness: number;
    reliability: number;
    gaps: string[];
    recommendations: string[];
  };
  
  // Competition analysis state
  competitorAnalysis?: {
    articleTypeAnalysis?: {
      primaryType: string;
      confidence: number;
      secondaryType?: string;
      reasoning: string;
      structure: {
        sections: string[];
        wordCountRange: number[];
        keyElements: string[];
      };
      engagementStrategies: string[];
      seoConsiderations: string[];
      visualElements: string[];
      competitorAnalysisNeeded: string[];
    };
    seoAnalysis: any;
    geoAnalysis: any;
    aeoAnalysis: any;
    contentGaps: {
      topicGaps: string[];
      depthGaps: string[];
      formatGaps: string[];
      audienceGaps: string[];
      successPatterns: {
        structuralElements: string[];
        engagementTactics: string[];
        qualityIndicators: string[];
        visualStrategies: string[];
      };
      improvementOpportunities: {
        qualityEnhancements: string[];
        uniqueAngles: string[];
        betterOrganization: string[];
        userExperience: string[];
      };
      competitiveIntelligence: {
        averageWordCount: number;
        commonHeadingStructure: string[];
        dataUsagePatterns: string[];
        linkingStrategies: string[];
      };
      actionableInsights: string[];
    };
    competitorStrengths?: {
      contentExcellence: {
        depthFactors: string[];
        credibilitySignals: string[];
        uniqueInsights: string[];
        practicalValue: string[];
      };
      engagementMastery: {
        hookStrategies: string[];
        narrativeFlow: string[];
        visualIntegration: string[];
        ctaOptimization: string[];
      };
      userExperienceSuperiority: {
        organizationMethods: string[];
        readabilityFactors: string[];
        mobileOptimization: string[];
        navigationExcellence: string[];
      };
      seoOptimizationExcellence: {
        keywordIntegration: string[];
        metaOptimization: string[];
        technicalSeo: string[];
        contentFreshness: string[];
      };
      innovationFactors: {
        uniqueFormats: string[];
        presentationMethods: string[];
        technologyIntegration: string[];
        engagementFeatures: string[];
      };
      keySuccessFactors: string[];
      implementationPriority: string[];
    };
    rankingFactors: any;
    writingPatterns: any;
  };
  
  // Content planning state
  contentPlan?: {
    articleType: string;
    structure: string[];
    keyPoints: string[];
    targetWordCount: number;
    seoStrategy: any;
  };
  
  // Content generation state
  generatedContent?: {
    title: string;
    content: string;
    metaDescription: string;
    keywords: string[];
    wordCount: number;
  };
  
  // Quality assurance state
  qualityReport?: {
    humanScore: number;
    seoScore: number;
    uniquenessScore: number;
    readabilityScore: number;
    factCheckReport: any;
    suggestions: string[];
  };
  
  // Quality assessment state (new for v2)
  qualityAssessment?: {
    aiDetectionResults: any;
    qualityResults: any;
    seoResults: any;
    readabilityResults: any;
    enhancementResults: any;
    overallScore: number;
    timestamp: number;
  };
  
  // Workflow state
  currentPhase: AgentPhase;
  completedPhases: AgentPhase[];
  errors: Array<{phase: string, error: string, timestamp: number}>;
  logs: Array<{agent: string, message: string, timestamp: number}>;
  
  // Execution metadata
  taskId: string;
  startTime: number;
  executionTime?: number;
  retryCount: number;
  maxRetries: number;
}

export enum AgentPhase {
  RESEARCH = 'research',
  COMPETITION_ANALYSIS = 'competition_analysis',
  CONTENT_PLANNING = 'content_planning',
  CONTENT_GENERATION = 'content_generation',
  QUALITY_ASSURANCE = 'quality_assurance',
  HUMAN_VALIDATION = 'human_validation',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export interface AgentNode {
  id: string;
  name: string;
  description: string;
  execute: (state: AgentState) => Promise<AgentState>;
  requiredPhases: AgentPhase[];
  outputPhase: AgentPhase;
  maxRetries: number;
  timeout: number;
}

export interface ResearchAgentConfig {
  searchDepth: number;
  maxUrls: number;
  parallelSearches: number;
  researchQueries: number;
  deepResearchEnabled?: boolean;
  iterativeRefinement?: boolean;
  multiStepAnalysis?: boolean;
  knowledgeGraphEnabled?: boolean;
  maxIterations?: number;
  qualityThreshold?: number;
}

export interface CompetitionAgentConfig {
  analysisDepth: 'basic' | 'comprehensive' | 'advanced';
  includeBacklinks: boolean;
  includeTechnicalSeo: boolean;
  includeContentGaps: boolean;
}

export interface WritingAgentConfig {
  humanizationLevel: 'low' | 'medium' | 'high' | 'maximum';
  creativityLevel: number; // 0-1
  seoOptimization: boolean;
  externalLinking: boolean;
  tableGeneration: boolean;
}

export interface QualityAgentConfig {
  aiDetectionBypass?: boolean;
  aiDetectionCheck?: boolean;
  factChecking?: boolean;
  factCheck?: boolean;
  grammarCheck?: boolean;
  readabilityCheck?: boolean;
  plagiarismCheck?: boolean;
  seoValidation?: boolean;
  qualityThreshold?: number;
}

export interface MultiAgentConfig {
  research: ResearchAgentConfig;
  competition: CompetitionAgentConfig;
  writing: WritingAgentConfig;
  quality: QualityAgentConfig;
  
  // Global settings
  maxExecutionTime: number;
  enableParallelProcessing: boolean;
  enableAdaptiveLearning: boolean;
  qualityThreshold: number;
  enableHumanLoop: boolean;
}

export interface AgentMessage {
  from: string;
  to: string;
  type: 'data' | 'request' | 'response' | 'error' | 'status';
  payload: any;
  timestamp: number;
}

export interface WorkflowStep {
  id: string;
  agent: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime?: number;
  endTime?: number;
  result?: any;
  error?: string;
}

export interface MultiAgentResult {
  success: boolean;
  finalState: AgentState;
  article?: {
    title: string;
    content: string;
    metaDescription: string;
    keywords: string[];
    wordCount: number;
  };
  qualityReport: {
    humanScore: number;
    seoScore: number;
    uniquenessScore: number;
    readabilityScore: number;
    overallScore: number;
  };
  researchSummary: {
    urlsAnalyzed: number;
    queriesExecuted: number;
    dataPoints: number;
    sourcesUsed: number;
  };
  competitionInsights: {
    competitorsAnalyzed: number;
    contentGapsFound: number;
    rankingOpportunities: string[];
  };
  executionMetrics: {
    totalTime: number;
    agentExecutionTimes: Record<string, number>;
    retryCount: number;
    failurePoints: string[];
  };
  logs: Array<{agent: string, message: string, timestamp: number}>;
  error?: string;
}

export interface AgentCapability {
  name: string;
  description: string;
  inputTypes: string[];
  outputTypes: string[];
  dependencies: string[];
  parallel: boolean;
}

export interface AgentMetrics {
  executionTime: number;
  successRate: number;
  qualityScore: number;
  retryCount: number;
  errorCount: number;
  lastExecution: number;
} 