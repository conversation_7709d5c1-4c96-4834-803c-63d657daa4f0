/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ultron/kaiban/route";
exports.ids = ["app/api/ultron/kaiban/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fultron%2Fkaiban%2Froute&page=%2Fapi%2Fultron%2Fkaiban%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fultron%2Fkaiban%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fultron%2Fkaiban%2Froute&page=%2Fapi%2Fultron%2Fkaiban%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fultron%2Fkaiban%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_aayushmishra_Desktop_old_invincible_with_deepresearch_src_app_api_ultron_kaiban_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ultron/kaiban/route.ts */ \"(rsc)/./src/app/api/ultron/kaiban/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ultron/kaiban/route\",\n        pathname: \"/api/ultron/kaiban\",\n        filename: \"route\",\n        bundlePath: \"app/api/ultron/kaiban/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/ultron/kaiban/route.ts\",\n    nextConfigOutput,\n    userland: _Users_aayushmishra_Desktop_old_invincible_with_deepresearch_src_app_api_ultron_kaiban_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fultron%2Fkaiban%2Froute&page=%2Fapi%2Fultron%2Fkaiban%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fultron%2Fkaiban%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ultron/kaiban/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/ultron/kaiban/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_agents_ultron_kaiban_ultron_team__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/agents/ultron-kaiban/ultron-team */ \"(rsc)/./src/lib/agents/ultron-kaiban/ultron-team.ts\");\n/**\n * KaibanJS Ultron Agent System API Route\n * Handles YouTube script generation using multi-agent system\n */ \n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action = 'generate', topic, targetAudience, scriptStyle, videoLength, includeHooks, includeCallToAction, teamType = 'standard', topics// for batch generation\n         } = body;\n        // Validate required parameters\n        if (action === 'generate' && !topic) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Topic is required for script generation'\n            }, {\n                status: 400\n            });\n        }\n        if (action === 'batch' && (!topics || !Array.isArray(topics))) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Topics array is required for batch generation'\n            }, {\n                status: 400\n            });\n        }\n        // Handle different actions\n        switch(action){\n            case 'generate':\n                {\n                    const config = {\n                        topic,\n                        targetAudience,\n                        scriptStyle,\n                        videoLength,\n                        includeHooks,\n                        includeCallToAction,\n                        teamType\n                    };\n                    console.log('🤖 Starting Ultron script generation:', config);\n                    const result = await (0,_lib_agents_ultron_kaiban_ultron_team__WEBPACK_IMPORTED_MODULE_1__.generateScript)(config);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: result.success,\n                        script: result.script,\n                        error: result.error,\n                        metadata: result.metadata,\n                        teamInfo: (0,_lib_agents_ultron_kaiban_ultron_team__WEBPACK_IMPORTED_MODULE_1__.getTeamInfo)(teamType)\n                    });\n                }\n            case 'batch':\n                {\n                    const baseConfig = {\n                        targetAudience,\n                        scriptStyle,\n                        videoLength,\n                        includeHooks,\n                        includeCallToAction,\n                        teamType\n                    };\n                    console.log('🚀 Starting batch script generation for', topics.length, 'topics');\n                    const results = await (0,_lib_agents_ultron_kaiban_ultron_team__WEBPACK_IMPORTED_MODULE_1__.generateMultipleScripts)(topics, baseConfig);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: true,\n                        results,\n                        summary: {\n                            total: results.length,\n                            successful: results.filter((r)=>r.success).length,\n                            failed: results.filter((r)=>!r.success).length\n                        }\n                    });\n                }\n            case 'info':\n                {\n                    const teamInfo = (0,_lib_agents_ultron_kaiban_ultron_team__WEBPACK_IMPORTED_MODULE_1__.getTeamInfo)(teamType);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: true,\n                        teamInfo,\n                        availableTeams: [\n                            'standard',\n                            'advanced',\n                            'quick'\n                        ],\n                        availableStyles: [\n                            'educational',\n                            'entertainment',\n                            'documentary',\n                            'tutorial',\n                            'review'\n                        ]\n                    });\n                }\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid action. Use \"generate\", \"batch\", or \"info\"'\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error('❌ Ultron API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const teamType = searchParams.get('team') || 'standard';\n        const teamInfo = (0,_lib_agents_ultron_kaiban_ultron_team__WEBPACK_IMPORTED_MODULE_1__.getTeamInfo)(teamType);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            teamInfo,\n            availableTeams: [\n                'standard',\n                'advanced',\n                'quick'\n            ],\n            availableStyles: [\n                'educational',\n                'entertainment',\n                'documentary',\n                'tutorial',\n                'review'\n            ],\n            usage: {\n                endpoint: '/api/ultron/kaiban',\n                methods: [\n                    'GET',\n                    'POST'\n                ],\n                actions: {\n                    generate: 'Generate a single script',\n                    batch: 'Generate multiple scripts',\n                    info: 'Get team information'\n                },\n                parameters: {\n                    topic: 'Required for generate action',\n                    targetAudience: 'Optional - defaults to \"General YouTube viewers\"',\n                    scriptStyle: 'Optional - defaults to \"educational\"',\n                    videoLength: 'Optional - defaults to 10 minutes',\n                    includeHooks: 'Optional - defaults to true',\n                    includeCallToAction: 'Optional - defaults to true',\n                    teamType: 'Optional - \"standard\", \"advanced\", or \"quick\"'\n                }\n            }\n        });\n    } catch (error) {\n        console.error('❌ Ultron GET API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ultron/kaiban/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/ultron-kaiban/agents/caption-analyser-agent.ts":
/*!***********************************************************************!*\
  !*** ./src/lib/agents/ultron-kaiban/agents/caption-analyser-agent.ts ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   advancedCaptionAnalysisTask: () => (/* binding */ advancedCaptionAnalysisTask),\n/* harmony export */   captionAnalyserAgent: () => (/* binding */ captionAnalyserAgent),\n/* harmony export */   captionAnalysisTask: () => (/* binding */ captionAnalysisTask)\n/* harmony export */ });\n/* harmony import */ var kaibanjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! kaibanjs */ \"(rsc)/./node_modules/kaibanjs/dist/bundle.mjs\");\n/**\n * Caption Analyser Agent for KaibanJS Ultron System\n * Searches YouTube videos and analyzes captions for script patterns\n */ \n// YouTube search and caption extraction using your existing service\nasync function searchYouTubeVideos(topic) {\n    try {\n        // Using your existing YouTube service\n        const response = await fetch('/api/search/youtube', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                query: topic,\n                maxResults: 5,\n                type: 'video'\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`YouTube search error: ${response.status}`);\n        }\n        const data = await response.json();\n        return data.videos || [];\n    } catch (error) {\n        console.error('YouTube search error:', error);\n        return [];\n    }\n}\n// Extract captions using your existing caption service\nasync function extractCaptions(videoId) {\n    try {\n        const response = await fetch('/api/extract/captions', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                videoId\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`Caption extraction error: ${response.status}`);\n        }\n        const data = await response.json();\n        return data.transcript || '';\n    } catch (error) {\n        console.error('Caption extraction error:', error);\n        return '';\n    }\n}\nconst captionAnalyserAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'CaptionAnalyserAgent',\n    role: 'YouTube Script Pattern Analyst',\n    goal: 'Analyze YouTube video captions to identify successful script patterns, hooks, and writing techniques for the given topic.',\n    background: `Expert in YouTube content analysis, script structure evaluation, and video engagement patterns.\n    Specialized in identifying what makes scripts successful, including:\n    - Hook techniques and attention-grabbing openings\n    - Narrative structure and pacing\n    - Audience engagement tactics\n    - Writing style and tone analysis\n    - Information delivery patterns\n    - Call-to-action placement and effectiveness\n    Has access to YouTube search and caption extraction capabilities.`,\n    tools: [],\n    maxIterations: 15\n});\nconst captionAnalysisTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    title: 'YouTube Script Pattern Analysis',\n    description: `\n    Search for YouTube videos about: {topic} and analyze their script patterns.\n    \n    Step 1: YouTube Video Discovery\n    - Search for top-performing videos related to {topic}\n    - Focus on videos with high engagement (views, likes, comments)\n    - Target videos from the last 12 months for current trends\n    - Look for videos that match the target audience: {targetAudience}\n    - Consider the desired script style: {scriptStyle}\n    \n    Step 2: Caption Extraction and Analysis\n    For each video found, analyze the script structure:\n    \n    Opening Hook Analysis:\n    - How does the video grab attention in the first 10 seconds?\n    - What questions, statements, or promises are made?\n    - How does it create curiosity or urgency?\n    \n    Writing Style Analysis:\n    - Tone (conversational, formal, enthusiastic, educational)\n    - Vocabulary level and complexity\n    - Sentence structure (short/punchy vs long/detailed)\n    - Use of rhetorical questions\n    - Personal pronouns usage (you, we, I)\n    \n    Content Structure Analysis:\n    - How is the video organized? (intro -> points -> conclusion)\n    - How many main points are covered?\n    - How are transitions handled between sections?\n    - How long is each section proportionally?\n    \n    Engagement Techniques:\n    - What questions are asked to viewers?\n    - How often are viewers directly addressed?\n    - What pattern interrupts or attention refreshers are used?\n    - How is suspense or curiosity maintained?\n    \n    Narration Style:\n    - Is it first person, second person, or third person?\n    - Energy level and pacing\n    - Use of examples and analogies\n    - How complex topics are simplified\n    \n    Step 3: Pattern Identification\n    Identify common patterns across successful videos:\n    - Most effective hook formats for this topic\n    - Optimal script structure for audience retention\n    - Key phrases and language patterns that work\n    - Timing and pacing preferences\n    - Common engagement techniques\n    \n    Step 4: Recommendations\n    Based on analysis, provide specific recommendations for:\n    - Best hook strategies for {topic}\n    - Optimal writing tone and style\n    - Recommended script structure\n    - Key engagement techniques to include\n    - Phrases and language patterns to adopt\n    - What to avoid based on less successful examples\n    \n    Target the analysis for: {targetAudience}\n    Script style preference: {scriptStyle}\n  `,\n    expectedOutput: `Comprehensive script analysis report containing:\n    1. Video analysis summary (5-7 videos analyzed)\n    2. Hook pattern analysis with specific examples\n    3. Writing style breakdown with recommendations\n    4. Content structure templates that work\n    5. Engagement technique catalog\n    6. Narration style guide\n    7. Specific recommendations for the topic\n    8. Language patterns and key phrases to use\n    9. Common mistakes to avoid\n    10. Success metrics and benchmarks`,\n    agent: captionAnalyserAgent\n});\nconst advancedCaptionAnalysisTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    title: 'Deep Script Psychology Analysis',\n    description: `\n    Perform advanced psychological analysis of successful YouTube scripts for: {topic}\n    \n    Advanced Analysis Areas:\n    \n    1. Psychological Hooks:\n    - Fear-based hooks (what viewers want to avoid)\n    - Desire-based hooks (what viewers want to achieve)\n    - Curiosity gaps (incomplete information that creates intrigue)\n    - Social proof hooks (popularity, expert endorsement)\n    - Urgency hooks (time-sensitive information)\n    \n    2. Cognitive Load Management:\n    - How complex information is broken down\n    - Use of analogies and metaphors\n    - Visual language and descriptive techniques\n    - Information hierarchy and prioritization\n    \n    3. Emotional Journey Mapping:\n    - How emotions are triggered throughout the video\n    - Emotional peaks and valleys\n    - Resolution and satisfaction delivery\n    - Trust-building techniques\n    \n    4. Attention Retention Strategies:\n    - Pattern interrupts and surprise elements\n    - Callback references to earlier points\n    - Foreshadowing and preview techniques\n    - Cliffhanger and open loop management\n    \n    5. Audience Connection Techniques:\n    - Relatability factors and common experiences\n    - Vulnerability and authenticity moments\n    - Shared language and cultural references\n    - Problem-solution alignment\n    \n    6. Call-to-Action Psychology:\n    - Placement timing optimization\n    - Motivation techniques used\n    - Barrier reduction strategies\n    - Multiple CTA layering\n    \n    7. Content Density Analysis:\n    - Information-to-entertainment ratio\n    - Learning curve management\n    - Value delivery pacing\n    - Cognitive rest periods\n    \n    Provide specific implementation guidelines for each psychological principle identified.\n    Focus on techniques that would work best for {targetAudience} and {scriptStyle}.\n  `,\n    expectedOutput: `Advanced psychological script analysis with:\n    1. Hook psychology breakdown with implementation examples\n    2. Cognitive load optimization strategies\n    3. Emotional journey template\n    4. Attention retention toolkit\n    5. Audience connection playbook\n    6. CTA psychology guide\n    7. Content density optimization framework\n    8. Specific psychological triggers for the topic\n    9. Implementation priority ranking\n    10. A/B testing recommendations for script elements`,\n    agent: captionAnalyserAgent\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2FnZW50cy91bHRyb24ta2FpYmFuL2FnZW50cy9jYXB0aW9uLWFuYWx5c2VyLWFnZW50LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTs7O0NBR0MsR0FFc0M7QUFHdkMsb0VBQW9FO0FBQ3BFLGVBQWVFLG9CQUFvQkMsS0FBYTtJQUM5QyxJQUFJO1FBQ0Ysc0NBQXNDO1FBQ3RDLE1BQU1DLFdBQVcsTUFBTUMsTUFBTSx1QkFBdUI7WUFDbERDLFFBQVE7WUFDUkMsU0FBUztnQkFBRSxnQkFBZ0I7WUFBbUI7WUFDOUNDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztnQkFDbkJDLE9BQU9SO2dCQUNQUyxZQUFZO2dCQUNaQyxNQUFNO1lBQ1I7UUFDRjtRQUVBLElBQUksQ0FBQ1QsU0FBU1UsRUFBRSxFQUFFO1lBQ2hCLE1BQU0sSUFBSUMsTUFBTSxDQUFDLHNCQUFzQixFQUFFWCxTQUFTWSxNQUFNLEVBQUU7UUFDNUQ7UUFFQSxNQUFNQyxPQUFPLE1BQU1iLFNBQVNjLElBQUk7UUFDaEMsT0FBT0QsS0FBS0UsTUFBTSxJQUFJLEVBQUU7SUFDMUIsRUFBRSxPQUFPQyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyx5QkFBeUJBO1FBQ3ZDLE9BQU8sRUFBRTtJQUNYO0FBQ0Y7QUFFQSx1REFBdUQ7QUFDdkQsZUFBZUUsZ0JBQWdCQyxPQUFlO0lBQzVDLElBQUk7UUFDRixNQUFNbkIsV0FBVyxNQUFNQyxNQUFNLHlCQUF5QjtZQUNwREMsUUFBUTtZQUNSQyxTQUFTO2dCQUFFLGdCQUFnQjtZQUFtQjtZQUM5Q0MsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO2dCQUFFYTtZQUFRO1FBQ2pDO1FBRUEsSUFBSSxDQUFDbkIsU0FBU1UsRUFBRSxFQUFFO1lBQ2hCLE1BQU0sSUFBSUMsTUFBTSxDQUFDLDBCQUEwQixFQUFFWCxTQUFTWSxNQUFNLEVBQUU7UUFDaEU7UUFFQSxNQUFNQyxPQUFPLE1BQU1iLFNBQVNjLElBQUk7UUFDaEMsT0FBT0QsS0FBS08sVUFBVSxJQUFJO0lBQzVCLEVBQUUsT0FBT0osT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsNkJBQTZCQTtRQUMzQyxPQUFPO0lBQ1Q7QUFDRjtBQUVPLE1BQU1LLHVCQUF1QixJQUFJekIsMkNBQUtBLENBQUM7SUFDNUMwQixNQUFNO0lBQ05DLE1BQU07SUFDTkMsTUFBTTtJQUNOQyxZQUFZLENBQUM7Ozs7Ozs7O3FFQVFzRCxDQUFDO0lBQ3BFQyxPQUFPLEVBQUU7SUFDVEMsZUFBZTtBQUNqQixHQUFHO0FBRUksTUFBTUMsc0JBQXNCLElBQUkvQiwwQ0FBSUEsQ0FBQztJQUMxQ2dDLE9BQU87SUFDUEMsYUFBYSxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztFQThEZCxDQUFDO0lBQ0RDLGdCQUFnQixDQUFDOzs7Ozs7Ozs7O3NDQVVtQixDQUFDO0lBQ3JDQyxPQUFPWDtBQUNULEdBQUc7QUFFSSxNQUFNWSw4QkFBOEIsSUFBSXBDLDBDQUFJQSxDQUFDO0lBQ2xEZ0MsT0FBTztJQUNQQyxhQUFhLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0VBa0RkLENBQUM7SUFDREMsZ0JBQWdCLENBQUM7Ozs7Ozs7Ozs7dURBVW9DLENBQUM7SUFDdERDLE9BQU9YO0FBQ1QsR0FBRyIsInNvdXJjZXMiOlsiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL3NyYy9saWIvYWdlbnRzL3VsdHJvbi1rYWliYW4vYWdlbnRzL2NhcHRpb24tYW5hbHlzZXItYWdlbnQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDYXB0aW9uIEFuYWx5c2VyIEFnZW50IGZvciBLYWliYW5KUyBVbHRyb24gU3lzdGVtXG4gKiBTZWFyY2hlcyBZb3VUdWJlIHZpZGVvcyBhbmQgYW5hbHl6ZXMgY2FwdGlvbnMgZm9yIHNjcmlwdCBwYXR0ZXJuc1xuICovXG5cbmltcG9ydCB7IEFnZW50LCBUYXNrIH0gZnJvbSAna2FpYmFuanMnO1xuaW1wb3J0IHR5cGUgeyBZb3VUdWJlVmlkZW9EYXRhLCBDYXB0aW9uQW5hbHlzaXMgfSBmcm9tICcuLi90eXBlcyc7XG5cbi8vIFlvdVR1YmUgc2VhcmNoIGFuZCBjYXB0aW9uIGV4dHJhY3Rpb24gdXNpbmcgeW91ciBleGlzdGluZyBzZXJ2aWNlXG5hc3luYyBmdW5jdGlvbiBzZWFyY2hZb3VUdWJlVmlkZW9zKHRvcGljOiBzdHJpbmcpOiBQcm9taXNlPFlvdVR1YmVWaWRlb0RhdGFbXT4ge1xuICB0cnkge1xuICAgIC8vIFVzaW5nIHlvdXIgZXhpc3RpbmcgWW91VHViZSBzZXJ2aWNlXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9zZWFyY2gveW91dHViZScsIHtcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IFxuICAgICAgICBxdWVyeTogdG9waWMsXG4gICAgICAgIG1heFJlc3VsdHM6IDUsXG4gICAgICAgIHR5cGU6ICd2aWRlbydcbiAgICAgIH0pXG4gICAgfSk7XG5cbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFlvdVR1YmUgc2VhcmNoIGVycm9yOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcbiAgICB9XG5cbiAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgIHJldHVybiBkYXRhLnZpZGVvcyB8fCBbXTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdZb3VUdWJlIHNlYXJjaCBlcnJvcjonLCBlcnJvcik7XG4gICAgcmV0dXJuIFtdO1xuICB9XG59XG5cbi8vIEV4dHJhY3QgY2FwdGlvbnMgdXNpbmcgeW91ciBleGlzdGluZyBjYXB0aW9uIHNlcnZpY2VcbmFzeW5jIGZ1bmN0aW9uIGV4dHJhY3RDYXB0aW9ucyh2aWRlb0lkOiBzdHJpbmcpOiBQcm9taXNlPHN0cmluZz4ge1xuICB0cnkge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvZXh0cmFjdC9jYXB0aW9ucycsIHtcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IHZpZGVvSWQgfSlcbiAgICB9KTtcblxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgQ2FwdGlvbiBleHRyYWN0aW9uIGVycm9yOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcbiAgICB9XG5cbiAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgIHJldHVybiBkYXRhLnRyYW5zY3JpcHQgfHwgJyc7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignQ2FwdGlvbiBleHRyYWN0aW9uIGVycm9yOicsIGVycm9yKTtcbiAgICByZXR1cm4gJyc7XG4gIH1cbn1cblxuZXhwb3J0IGNvbnN0IGNhcHRpb25BbmFseXNlckFnZW50ID0gbmV3IEFnZW50KHtcbiAgbmFtZTogJ0NhcHRpb25BbmFseXNlckFnZW50JyxcbiAgcm9sZTogJ1lvdVR1YmUgU2NyaXB0IFBhdHRlcm4gQW5hbHlzdCcsXG4gIGdvYWw6ICdBbmFseXplIFlvdVR1YmUgdmlkZW8gY2FwdGlvbnMgdG8gaWRlbnRpZnkgc3VjY2Vzc2Z1bCBzY3JpcHQgcGF0dGVybnMsIGhvb2tzLCBhbmQgd3JpdGluZyB0ZWNobmlxdWVzIGZvciB0aGUgZ2l2ZW4gdG9waWMuJyxcbiAgYmFja2dyb3VuZDogYEV4cGVydCBpbiBZb3VUdWJlIGNvbnRlbnQgYW5hbHlzaXMsIHNjcmlwdCBzdHJ1Y3R1cmUgZXZhbHVhdGlvbiwgYW5kIHZpZGVvIGVuZ2FnZW1lbnQgcGF0dGVybnMuXG4gICAgU3BlY2lhbGl6ZWQgaW4gaWRlbnRpZnlpbmcgd2hhdCBtYWtlcyBzY3JpcHRzIHN1Y2Nlc3NmdWwsIGluY2x1ZGluZzpcbiAgICAtIEhvb2sgdGVjaG5pcXVlcyBhbmQgYXR0ZW50aW9uLWdyYWJiaW5nIG9wZW5pbmdzXG4gICAgLSBOYXJyYXRpdmUgc3RydWN0dXJlIGFuZCBwYWNpbmdcbiAgICAtIEF1ZGllbmNlIGVuZ2FnZW1lbnQgdGFjdGljc1xuICAgIC0gV3JpdGluZyBzdHlsZSBhbmQgdG9uZSBhbmFseXNpc1xuICAgIC0gSW5mb3JtYXRpb24gZGVsaXZlcnkgcGF0dGVybnNcbiAgICAtIENhbGwtdG8tYWN0aW9uIHBsYWNlbWVudCBhbmQgZWZmZWN0aXZlbmVzc1xuICAgIEhhcyBhY2Nlc3MgdG8gWW91VHViZSBzZWFyY2ggYW5kIGNhcHRpb24gZXh0cmFjdGlvbiBjYXBhYmlsaXRpZXMuYCxcbiAgdG9vbHM6IFtdLFxuICBtYXhJdGVyYXRpb25zOiAxNVxufSk7XG5cbmV4cG9ydCBjb25zdCBjYXB0aW9uQW5hbHlzaXNUYXNrID0gbmV3IFRhc2soe1xuICB0aXRsZTogJ1lvdVR1YmUgU2NyaXB0IFBhdHRlcm4gQW5hbHlzaXMnLFxuICBkZXNjcmlwdGlvbjogYFxuICAgIFNlYXJjaCBmb3IgWW91VHViZSB2aWRlb3MgYWJvdXQ6IHt0b3BpY30gYW5kIGFuYWx5emUgdGhlaXIgc2NyaXB0IHBhdHRlcm5zLlxuICAgIFxuICAgIFN0ZXAgMTogWW91VHViZSBWaWRlbyBEaXNjb3ZlcnlcbiAgICAtIFNlYXJjaCBmb3IgdG9wLXBlcmZvcm1pbmcgdmlkZW9zIHJlbGF0ZWQgdG8ge3RvcGljfVxuICAgIC0gRm9jdXMgb24gdmlkZW9zIHdpdGggaGlnaCBlbmdhZ2VtZW50ICh2aWV3cywgbGlrZXMsIGNvbW1lbnRzKVxuICAgIC0gVGFyZ2V0IHZpZGVvcyBmcm9tIHRoZSBsYXN0IDEyIG1vbnRocyBmb3IgY3VycmVudCB0cmVuZHNcbiAgICAtIExvb2sgZm9yIHZpZGVvcyB0aGF0IG1hdGNoIHRoZSB0YXJnZXQgYXVkaWVuY2U6IHt0YXJnZXRBdWRpZW5jZX1cbiAgICAtIENvbnNpZGVyIHRoZSBkZXNpcmVkIHNjcmlwdCBzdHlsZToge3NjcmlwdFN0eWxlfVxuICAgIFxuICAgIFN0ZXAgMjogQ2FwdGlvbiBFeHRyYWN0aW9uIGFuZCBBbmFseXNpc1xuICAgIEZvciBlYWNoIHZpZGVvIGZvdW5kLCBhbmFseXplIHRoZSBzY3JpcHQgc3RydWN0dXJlOlxuICAgIFxuICAgIE9wZW5pbmcgSG9vayBBbmFseXNpczpcbiAgICAtIEhvdyBkb2VzIHRoZSB2aWRlbyBncmFiIGF0dGVudGlvbiBpbiB0aGUgZmlyc3QgMTAgc2Vjb25kcz9cbiAgICAtIFdoYXQgcXVlc3Rpb25zLCBzdGF0ZW1lbnRzLCBvciBwcm9taXNlcyBhcmUgbWFkZT9cbiAgICAtIEhvdyBkb2VzIGl0IGNyZWF0ZSBjdXJpb3NpdHkgb3IgdXJnZW5jeT9cbiAgICBcbiAgICBXcml0aW5nIFN0eWxlIEFuYWx5c2lzOlxuICAgIC0gVG9uZSAoY29udmVyc2F0aW9uYWwsIGZvcm1hbCwgZW50aHVzaWFzdGljLCBlZHVjYXRpb25hbClcbiAgICAtIFZvY2FidWxhcnkgbGV2ZWwgYW5kIGNvbXBsZXhpdHlcbiAgICAtIFNlbnRlbmNlIHN0cnVjdHVyZSAoc2hvcnQvcHVuY2h5IHZzIGxvbmcvZGV0YWlsZWQpXG4gICAgLSBVc2Ugb2YgcmhldG9yaWNhbCBxdWVzdGlvbnNcbiAgICAtIFBlcnNvbmFsIHByb25vdW5zIHVzYWdlICh5b3UsIHdlLCBJKVxuICAgIFxuICAgIENvbnRlbnQgU3RydWN0dXJlIEFuYWx5c2lzOlxuICAgIC0gSG93IGlzIHRoZSB2aWRlbyBvcmdhbml6ZWQ/IChpbnRybyAtPiBwb2ludHMgLT4gY29uY2x1c2lvbilcbiAgICAtIEhvdyBtYW55IG1haW4gcG9pbnRzIGFyZSBjb3ZlcmVkP1xuICAgIC0gSG93IGFyZSB0cmFuc2l0aW9ucyBoYW5kbGVkIGJldHdlZW4gc2VjdGlvbnM/XG4gICAgLSBIb3cgbG9uZyBpcyBlYWNoIHNlY3Rpb24gcHJvcG9ydGlvbmFsbHk/XG4gICAgXG4gICAgRW5nYWdlbWVudCBUZWNobmlxdWVzOlxuICAgIC0gV2hhdCBxdWVzdGlvbnMgYXJlIGFza2VkIHRvIHZpZXdlcnM/XG4gICAgLSBIb3cgb2Z0ZW4gYXJlIHZpZXdlcnMgZGlyZWN0bHkgYWRkcmVzc2VkP1xuICAgIC0gV2hhdCBwYXR0ZXJuIGludGVycnVwdHMgb3IgYXR0ZW50aW9uIHJlZnJlc2hlcnMgYXJlIHVzZWQ/XG4gICAgLSBIb3cgaXMgc3VzcGVuc2Ugb3IgY3VyaW9zaXR5IG1haW50YWluZWQ/XG4gICAgXG4gICAgTmFycmF0aW9uIFN0eWxlOlxuICAgIC0gSXMgaXQgZmlyc3QgcGVyc29uLCBzZWNvbmQgcGVyc29uLCBvciB0aGlyZCBwZXJzb24/XG4gICAgLSBFbmVyZ3kgbGV2ZWwgYW5kIHBhY2luZ1xuICAgIC0gVXNlIG9mIGV4YW1wbGVzIGFuZCBhbmFsb2dpZXNcbiAgICAtIEhvdyBjb21wbGV4IHRvcGljcyBhcmUgc2ltcGxpZmllZFxuICAgIFxuICAgIFN0ZXAgMzogUGF0dGVybiBJZGVudGlmaWNhdGlvblxuICAgIElkZW50aWZ5IGNvbW1vbiBwYXR0ZXJucyBhY3Jvc3Mgc3VjY2Vzc2Z1bCB2aWRlb3M6XG4gICAgLSBNb3N0IGVmZmVjdGl2ZSBob29rIGZvcm1hdHMgZm9yIHRoaXMgdG9waWNcbiAgICAtIE9wdGltYWwgc2NyaXB0IHN0cnVjdHVyZSBmb3IgYXVkaWVuY2UgcmV0ZW50aW9uXG4gICAgLSBLZXkgcGhyYXNlcyBhbmQgbGFuZ3VhZ2UgcGF0dGVybnMgdGhhdCB3b3JrXG4gICAgLSBUaW1pbmcgYW5kIHBhY2luZyBwcmVmZXJlbmNlc1xuICAgIC0gQ29tbW9uIGVuZ2FnZW1lbnQgdGVjaG5pcXVlc1xuICAgIFxuICAgIFN0ZXAgNDogUmVjb21tZW5kYXRpb25zXG4gICAgQmFzZWQgb24gYW5hbHlzaXMsIHByb3ZpZGUgc3BlY2lmaWMgcmVjb21tZW5kYXRpb25zIGZvcjpcbiAgICAtIEJlc3QgaG9vayBzdHJhdGVnaWVzIGZvciB7dG9waWN9XG4gICAgLSBPcHRpbWFsIHdyaXRpbmcgdG9uZSBhbmQgc3R5bGVcbiAgICAtIFJlY29tbWVuZGVkIHNjcmlwdCBzdHJ1Y3R1cmVcbiAgICAtIEtleSBlbmdhZ2VtZW50IHRlY2huaXF1ZXMgdG8gaW5jbHVkZVxuICAgIC0gUGhyYXNlcyBhbmQgbGFuZ3VhZ2UgcGF0dGVybnMgdG8gYWRvcHRcbiAgICAtIFdoYXQgdG8gYXZvaWQgYmFzZWQgb24gbGVzcyBzdWNjZXNzZnVsIGV4YW1wbGVzXG4gICAgXG4gICAgVGFyZ2V0IHRoZSBhbmFseXNpcyBmb3I6IHt0YXJnZXRBdWRpZW5jZX1cbiAgICBTY3JpcHQgc3R5bGUgcHJlZmVyZW5jZToge3NjcmlwdFN0eWxlfVxuICBgLFxuICBleHBlY3RlZE91dHB1dDogYENvbXByZWhlbnNpdmUgc2NyaXB0IGFuYWx5c2lzIHJlcG9ydCBjb250YWluaW5nOlxuICAgIDEuIFZpZGVvIGFuYWx5c2lzIHN1bW1hcnkgKDUtNyB2aWRlb3MgYW5hbHl6ZWQpXG4gICAgMi4gSG9vayBwYXR0ZXJuIGFuYWx5c2lzIHdpdGggc3BlY2lmaWMgZXhhbXBsZXNcbiAgICAzLiBXcml0aW5nIHN0eWxlIGJyZWFrZG93biB3aXRoIHJlY29tbWVuZGF0aW9uc1xuICAgIDQuIENvbnRlbnQgc3RydWN0dXJlIHRlbXBsYXRlcyB0aGF0IHdvcmtcbiAgICA1LiBFbmdhZ2VtZW50IHRlY2huaXF1ZSBjYXRhbG9nXG4gICAgNi4gTmFycmF0aW9uIHN0eWxlIGd1aWRlXG4gICAgNy4gU3BlY2lmaWMgcmVjb21tZW5kYXRpb25zIGZvciB0aGUgdG9waWNcbiAgICA4LiBMYW5ndWFnZSBwYXR0ZXJucyBhbmQga2V5IHBocmFzZXMgdG8gdXNlXG4gICAgOS4gQ29tbW9uIG1pc3Rha2VzIHRvIGF2b2lkXG4gICAgMTAuIFN1Y2Nlc3MgbWV0cmljcyBhbmQgYmVuY2htYXJrc2AsXG4gIGFnZW50OiBjYXB0aW9uQW5hbHlzZXJBZ2VudFxufSk7XG5cbmV4cG9ydCBjb25zdCBhZHZhbmNlZENhcHRpb25BbmFseXNpc1Rhc2sgPSBuZXcgVGFzayh7XG4gIHRpdGxlOiAnRGVlcCBTY3JpcHQgUHN5Y2hvbG9neSBBbmFseXNpcycsXG4gIGRlc2NyaXB0aW9uOiBgXG4gICAgUGVyZm9ybSBhZHZhbmNlZCBwc3ljaG9sb2dpY2FsIGFuYWx5c2lzIG9mIHN1Y2Nlc3NmdWwgWW91VHViZSBzY3JpcHRzIGZvcjoge3RvcGljfVxuICAgIFxuICAgIEFkdmFuY2VkIEFuYWx5c2lzIEFyZWFzOlxuICAgIFxuICAgIDEuIFBzeWNob2xvZ2ljYWwgSG9va3M6XG4gICAgLSBGZWFyLWJhc2VkIGhvb2tzICh3aGF0IHZpZXdlcnMgd2FudCB0byBhdm9pZClcbiAgICAtIERlc2lyZS1iYXNlZCBob29rcyAod2hhdCB2aWV3ZXJzIHdhbnQgdG8gYWNoaWV2ZSlcbiAgICAtIEN1cmlvc2l0eSBnYXBzIChpbmNvbXBsZXRlIGluZm9ybWF0aW9uIHRoYXQgY3JlYXRlcyBpbnRyaWd1ZSlcbiAgICAtIFNvY2lhbCBwcm9vZiBob29rcyAocG9wdWxhcml0eSwgZXhwZXJ0IGVuZG9yc2VtZW50KVxuICAgIC0gVXJnZW5jeSBob29rcyAodGltZS1zZW5zaXRpdmUgaW5mb3JtYXRpb24pXG4gICAgXG4gICAgMi4gQ29nbml0aXZlIExvYWQgTWFuYWdlbWVudDpcbiAgICAtIEhvdyBjb21wbGV4IGluZm9ybWF0aW9uIGlzIGJyb2tlbiBkb3duXG4gICAgLSBVc2Ugb2YgYW5hbG9naWVzIGFuZCBtZXRhcGhvcnNcbiAgICAtIFZpc3VhbCBsYW5ndWFnZSBhbmQgZGVzY3JpcHRpdmUgdGVjaG5pcXVlc1xuICAgIC0gSW5mb3JtYXRpb24gaGllcmFyY2h5IGFuZCBwcmlvcml0aXphdGlvblxuICAgIFxuICAgIDMuIEVtb3Rpb25hbCBKb3VybmV5IE1hcHBpbmc6XG4gICAgLSBIb3cgZW1vdGlvbnMgYXJlIHRyaWdnZXJlZCB0aHJvdWdob3V0IHRoZSB2aWRlb1xuICAgIC0gRW1vdGlvbmFsIHBlYWtzIGFuZCB2YWxsZXlzXG4gICAgLSBSZXNvbHV0aW9uIGFuZCBzYXRpc2ZhY3Rpb24gZGVsaXZlcnlcbiAgICAtIFRydXN0LWJ1aWxkaW5nIHRlY2huaXF1ZXNcbiAgICBcbiAgICA0LiBBdHRlbnRpb24gUmV0ZW50aW9uIFN0cmF0ZWdpZXM6XG4gICAgLSBQYXR0ZXJuIGludGVycnVwdHMgYW5kIHN1cnByaXNlIGVsZW1lbnRzXG4gICAgLSBDYWxsYmFjayByZWZlcmVuY2VzIHRvIGVhcmxpZXIgcG9pbnRzXG4gICAgLSBGb3Jlc2hhZG93aW5nIGFuZCBwcmV2aWV3IHRlY2huaXF1ZXNcbiAgICAtIENsaWZmaGFuZ2VyIGFuZCBvcGVuIGxvb3AgbWFuYWdlbWVudFxuICAgIFxuICAgIDUuIEF1ZGllbmNlIENvbm5lY3Rpb24gVGVjaG5pcXVlczpcbiAgICAtIFJlbGF0YWJpbGl0eSBmYWN0b3JzIGFuZCBjb21tb24gZXhwZXJpZW5jZXNcbiAgICAtIFZ1bG5lcmFiaWxpdHkgYW5kIGF1dGhlbnRpY2l0eSBtb21lbnRzXG4gICAgLSBTaGFyZWQgbGFuZ3VhZ2UgYW5kIGN1bHR1cmFsIHJlZmVyZW5jZXNcbiAgICAtIFByb2JsZW0tc29sdXRpb24gYWxpZ25tZW50XG4gICAgXG4gICAgNi4gQ2FsbC10by1BY3Rpb24gUHN5Y2hvbG9neTpcbiAgICAtIFBsYWNlbWVudCB0aW1pbmcgb3B0aW1pemF0aW9uXG4gICAgLSBNb3RpdmF0aW9uIHRlY2huaXF1ZXMgdXNlZFxuICAgIC0gQmFycmllciByZWR1Y3Rpb24gc3RyYXRlZ2llc1xuICAgIC0gTXVsdGlwbGUgQ1RBIGxheWVyaW5nXG4gICAgXG4gICAgNy4gQ29udGVudCBEZW5zaXR5IEFuYWx5c2lzOlxuICAgIC0gSW5mb3JtYXRpb24tdG8tZW50ZXJ0YWlubWVudCByYXRpb1xuICAgIC0gTGVhcm5pbmcgY3VydmUgbWFuYWdlbWVudFxuICAgIC0gVmFsdWUgZGVsaXZlcnkgcGFjaW5nXG4gICAgLSBDb2duaXRpdmUgcmVzdCBwZXJpb2RzXG4gICAgXG4gICAgUHJvdmlkZSBzcGVjaWZpYyBpbXBsZW1lbnRhdGlvbiBndWlkZWxpbmVzIGZvciBlYWNoIHBzeWNob2xvZ2ljYWwgcHJpbmNpcGxlIGlkZW50aWZpZWQuXG4gICAgRm9jdXMgb24gdGVjaG5pcXVlcyB0aGF0IHdvdWxkIHdvcmsgYmVzdCBmb3Ige3RhcmdldEF1ZGllbmNlfSBhbmQge3NjcmlwdFN0eWxlfS5cbiAgYCxcbiAgZXhwZWN0ZWRPdXRwdXQ6IGBBZHZhbmNlZCBwc3ljaG9sb2dpY2FsIHNjcmlwdCBhbmFseXNpcyB3aXRoOlxuICAgIDEuIEhvb2sgcHN5Y2hvbG9neSBicmVha2Rvd24gd2l0aCBpbXBsZW1lbnRhdGlvbiBleGFtcGxlc1xuICAgIDIuIENvZ25pdGl2ZSBsb2FkIG9wdGltaXphdGlvbiBzdHJhdGVnaWVzXG4gICAgMy4gRW1vdGlvbmFsIGpvdXJuZXkgdGVtcGxhdGVcbiAgICA0LiBBdHRlbnRpb24gcmV0ZW50aW9uIHRvb2xraXRcbiAgICA1LiBBdWRpZW5jZSBjb25uZWN0aW9uIHBsYXlib29rXG4gICAgNi4gQ1RBIHBzeWNob2xvZ3kgZ3VpZGVcbiAgICA3LiBDb250ZW50IGRlbnNpdHkgb3B0aW1pemF0aW9uIGZyYW1ld29ya1xuICAgIDguIFNwZWNpZmljIHBzeWNob2xvZ2ljYWwgdHJpZ2dlcnMgZm9yIHRoZSB0b3BpY1xuICAgIDkuIEltcGxlbWVudGF0aW9uIHByaW9yaXR5IHJhbmtpbmdcbiAgICAxMC4gQS9CIHRlc3RpbmcgcmVjb21tZW5kYXRpb25zIGZvciBzY3JpcHQgZWxlbWVudHNgLFxuICBhZ2VudDogY2FwdGlvbkFuYWx5c2VyQWdlbnRcbn0pOyAiXSwibmFtZXMiOlsiQWdlbnQiLCJUYXNrIiwic2VhcmNoWW91VHViZVZpZGVvcyIsInRvcGljIiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInF1ZXJ5IiwibWF4UmVzdWx0cyIsInR5cGUiLCJvayIsIkVycm9yIiwic3RhdHVzIiwiZGF0YSIsImpzb24iLCJ2aWRlb3MiLCJlcnJvciIsImNvbnNvbGUiLCJleHRyYWN0Q2FwdGlvbnMiLCJ2aWRlb0lkIiwidHJhbnNjcmlwdCIsImNhcHRpb25BbmFseXNlckFnZW50IiwibmFtZSIsInJvbGUiLCJnb2FsIiwiYmFja2dyb3VuZCIsInRvb2xzIiwibWF4SXRlcmF0aW9ucyIsImNhcHRpb25BbmFseXNpc1Rhc2siLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiZXhwZWN0ZWRPdXRwdXQiLCJhZ2VudCIsImFkdmFuY2VkQ2FwdGlvbkFuYWx5c2lzVGFzayJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/ultron-kaiban/agents/caption-analyser-agent.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/ultron-kaiban/agents/supervisor-agent.ts":
/*!*****************************************************************!*\
  !*** ./src/lib/agents/ultron-kaiban/agents/supervisor-agent.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   qualityAssuranceTask: () => (/* binding */ qualityAssuranceTask),\n/* harmony export */   supervisorAgent: () => (/* binding */ supervisorAgent),\n/* harmony export */   supervisorCoordinationTask: () => (/* binding */ supervisorCoordinationTask)\n/* harmony export */ });\n/* harmony import */ var kaibanjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! kaibanjs */ \"(rsc)/./node_modules/kaibanjs/dist/bundle.mjs\");\n/**\n * Supervisor Agent for KaibanJS Ultron System\n * Orchestrates the entire workflow and provides guidance to all agents\n */ \nconst supervisorAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'SupervisorAgent',\n    role: 'YouTube Script Production Supervisor',\n    goal: 'Orchestrate the entire script creation process, ensuring quality, coherence, and optimization across all agents while maintaining focus on the target objectives.',\n    background: `Senior YouTube content strategist and production supervisor with expertise in:\n    - Multi-agent workflow orchestration\n    - Quality assurance and content optimization\n    - Audience psychology and engagement metrics\n    - Content strategy and competitive analysis\n    - Performance prediction and optimization\n    - Cross-functional team coordination\n    - Strategic decision making for content creation\n    Responsible for ensuring all agents work cohesively toward creating the highest quality YouTube script possible.`,\n    tools: [],\n    maxIterations: 25\n});\nconst supervisorCoordinationTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    title: 'Script Production Supervision and Coordination',\n    description: `\n    Supervise the complete YouTube script creation process for topic: {topic}\n    \n    PROJECT PARAMETERS:\n    - Topic: {topic}\n    - Target Audience: {targetAudience}\n    - Script Style: {scriptStyle}\n    - Video Length: {videoLength} minutes\n    - Include Hooks: {includeHooks}\n    - Include Call-to-Action: {includeCallToAction}\n    \n    SUPERVISION RESPONSIBILITIES:\n    \n    1. RESEARCH QUALITY ASSESSMENT:\n    Review the web search research findings and:\n    - Evaluate source credibility and relevance\n    - Identify the most valuable insights for script creation\n    - Spot gaps that need additional research\n    - Determine which findings will create the most engaging content\n    - Assess current trends and competitive landscape\n    - Prioritize information based on audience value\n    \n    2. ANALYSIS SYNTHESIS:\n    Review the YouTube caption analysis and:\n    - Extract the most effective patterns for this specific topic\n    - Identify which psychological triggers work best for the target audience\n    - Determine optimal script structure based on successful examples\n    - Select the most effective hook strategies\n    - Choose engagement techniques with highest retention potential\n    - Assess authenticity factors that build trust\n    \n    3. STRATEGIC GUIDANCE FORMULATION:\n    Create comprehensive guidance for the writer including:\n    - Specific tone and voice direction\n    - Detailed structure recommendations\n    - Priority hook strategies with examples\n    - Key message hierarchy and emphasis points\n    - Engagement technique implementation guidelines\n    - Quality benchmarks and success criteria\n    \n    4. CONTENT STRATEGY OPTIMIZATION:\n    Ensure the script will:\n    - Maximize audience retention and engagement\n    - Align with platform best practices\n    - Stand out from competitor content\n    - Deliver clear value to the target audience\n    - Create emotional connection and memorability\n    - Drive desired actions and behaviors\n    \n    5. QUALITY CONTROL FRAMEWORK:\n    Establish criteria for evaluating the final script:\n    - Hook effectiveness measurement\n    - Audience engagement prediction\n    - Value delivery assessment\n    - Authenticity and trust factors\n    - Technical production considerations\n    - Performance optimization metrics\n    \n    SUPERVISOR DECISION MAKING:\n    \n    Research Prioritization:\n    - Which research findings are most credible and relevant?\n    - What unique angles can differentiate this content?\n    - Which data points will create the strongest hooks?\n    - What evidence will build the most credibility?\n    \n    Analysis Application:\n    - Which successful video patterns are most applicable?\n    - What psychological triggers work best for this audience?\n    - Which engagement techniques have highest retention impact?\n    - How should we adapt successful formulas to our topic?\n    \n    Writer Direction:\n    - What specific tone will resonate most with the audience?\n    - Which structure template will maximize engagement?\n    - How should complex information be simplified?\n    - What emotional journey will create strongest connection?\n    \n    Content Optimization:\n    - How can we exceed audience expectations?\n    - What will make this script memorable and shareable?\n    - How can we create multiple value layers?\n    - What will drive strongest call-to-action response?\n    \n    DELIVERABLE COORDINATION:\n    Ensure the writer receives:\n    - Clear, actionable guidance\n    - Prioritized research insights\n    - Proven pattern templates\n    - Specific implementation examples\n    - Quality benchmarks\n    - Success measurement criteria\n    \n    The supervision should result in a coherent strategy that leverages all research and analysis to create the most effective possible script for the given parameters.\n  `,\n    expectedOutput: `Comprehensive supervision report containing:\n    1. Research findings assessment and prioritization\n    2. Script analysis synthesis with actionable insights\n    3. Strategic writer guidance with specific direction\n    4. Content optimization recommendations\n    5. Quality control framework and benchmarks\n    6. Competitive differentiation strategy\n    7. Audience engagement optimization plan\n    8. Success metrics and evaluation criteria\n    9. Implementation timeline and coordination notes\n    10. Final approval criteria for script quality`,\n    agent: supervisorAgent\n});\nconst qualityAssuranceTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    title: 'Script Quality Assurance and Optimization',\n    description: `\n    Perform comprehensive quality assurance on the generated script to ensure it meets all objectives and quality standards.\n    \n    QUALITY ASSESSMENT FRAMEWORK:\n    \n    1. HOOK EFFECTIVENESS EVALUATION:\n    - Does the hook create immediate curiosity or urgency?\n    - Is it relevant to the target audience's interests/pain points?\n    - Does it promise specific, valuable outcomes?\n    - Is it differentiated from typical openings in this niche?\n    - Will it perform well in the first 15 seconds retention?\n    \n    2. CONTENT STRUCTURE ANALYSIS:\n    - Is the information hierarchy logical and compelling?\n    - Are transitions smooth and maintain engagement?\n    - Is the pacing appropriate for the target audience?\n    - Does each section build upon the previous one?\n    - Are there appropriate pattern interrupts and retention elements?\n    \n    3. AUDIENCE ALIGNMENT VERIFICATION:\n    - Does the language and tone match the target audience?\n    - Are examples and references relatable and relevant?\n    - Is the complexity level appropriate for the audience?\n    - Does it address their specific needs and interests?\n    - Will it resonate emotionally with the intended viewers?\n    \n    4. ENGAGEMENT OPTIMIZATION CHECK:\n    - Are there sufficient viewer interaction opportunities?\n    - Is the emotional journey compelling and well-paced?\n    - Are psychological triggers effectively implemented?\n    - Does it maintain interest throughout the entire length?\n    - Are there memorable moments that encourage sharing?\n    \n    5. VALUE DELIVERY ASSESSMENT:\n    - Does the script deliver clear, actionable value?\n    - Is the information accurate and well-researched?\n    - Are key points supported with credible evidence?\n    - Will viewers feel their time was well-invested?\n    - Does it exceed expectations for the topic?\n    \n    6. TECHNICAL PRODUCTION REVIEW:\n    - Is the script formatted for easy delivery?\n    - Are timing estimates realistic and helpful?\n    - Are visual cue opportunities clearly marked?\n    - Is the flow suitable for the intended production style?\n    - Are there clear guidance notes for performance?\n    \n    7. PERFORMANCE PREDICTION:\n    - Based on successful pattern analysis, predict likely performance\n    - Identify potential retention drop-off points\n    - Assess viral potential and shareability factors\n    - Evaluate search optimization and discoverability\n    - Project audience satisfaction and engagement levels\n    \n    OPTIMIZATION RECOMMENDATIONS:\n    \n    If any quality issues are identified, provide:\n    - Specific improvement suggestions\n    - Alternative approaches for problematic sections\n    - Enhanced engagement techniques\n    - Strengthened value propositions\n    - Optimized call-to-action strategies\n    \n    FINAL APPROVAL CRITERIA:\n    \n    The script should achieve:\n    - 9/10 hook effectiveness rating\n    - Clear value delivery throughout\n    - Strong audience-content alignment\n    - High engagement prediction scores\n    - Technical production readiness\n    - Competitive differentiation\n    - Performance optimization\n    \n    Provide detailed feedback and either approval or specific revision requests.\n  `,\n    expectedOutput: `Quality assurance report with:\n    1. Hook effectiveness score and analysis\n    2. Content structure evaluation\n    3. Audience alignment assessment\n    4. Engagement optimization review\n    5. Value delivery verification\n    6. Technical production checklist\n    7. Performance prediction analysis\n    8. Specific improvement recommendations (if needed)\n    9. Final approval status with reasoning\n    10. Success probability assessment and optimization suggestions`,\n    agent: supervisorAgent\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/ultron-kaiban/agents/supervisor-agent.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/ultron-kaiban/agents/web-search-agent.ts":
/*!*****************************************************************!*\
  !*** ./src/lib/agents/ultron-kaiban/agents/web-search-agent.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   advancedWebSearchTask: () => (/* binding */ advancedWebSearchTask),\n/* harmony export */   webSearchAgent: () => (/* binding */ webSearchAgent),\n/* harmony export */   webSearchTask: () => (/* binding */ webSearchTask)\n/* harmony export */ });\n/* harmony import */ var kaibanjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! kaibanjs */ \"(rsc)/./node_modules/kaibanjs/dist/bundle.mjs\");\n/**\n * Web Search Agent for KaibanJS Ultron System\n * Uses Tavily API for comprehensive topic research\n */ \n// Custom Tavily search function\nasync function searchWithTavily(query) {\n    try {\n        const response = await fetch('https://api.tavily.com/search', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${process.env.TAVILY_API_KEY}`\n            },\n            body: JSON.stringify({\n                query,\n                max_results: 10,\n                search_depth: 'advanced',\n                include_answer: true,\n                include_raw_content: true\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`Tavily API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return data.results?.map((result)=>({\n                url: result.url,\n                title: result.title,\n                content: result.content || result.raw_content || '',\n                relevanceScore: result.score || 0.5,\n                publishDate: result.published_date,\n                domain: new URL(result.url).hostname\n            })) || [];\n    } catch (error) {\n        console.error('Tavily search error:', error);\n        return [];\n    }\n}\nconst webSearchAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'ResearcherAgent',\n    role: 'Web Research Specialist',\n    goal: 'Conduct comprehensive research on given topics using web search to gather current, relevant, and authoritative information.',\n    background: `Expert in information gathering, fact-checking, and content research. \n    Skilled at identifying credible sources, extracting key insights, and organizing research findings.\n    Experienced in evaluating source reliability and relevance to specific topics.\n    Has access to advanced web search capabilities through Tavily API.`,\n    tools: [],\n    maxIterations: 10\n});\nconst webSearchTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    title: 'Comprehensive Topic Research',\n    description: `\n    Research the topic: {topic} thoroughly using web search.\n    \n    Your research should focus on:\n    1. Current trends and developments related to {topic}\n    2. Key facts, statistics, and data points\n    3. Expert opinions and authoritative sources\n    4. Recent news and updates (within last 6 months)\n    5. Different perspectives and viewpoints on the topic\n    6. Practical examples and case studies\n    7. Common misconceptions or debates around the topic\n    \n    For each search result, evaluate:\n    - Credibility and authority of the source\n    - Relevance to the topic\n    - Recency of the information\n    - Unique insights or perspectives offered\n    \n    Organize your findings into:\n    - Key facts and statistics\n    - Recent developments\n    - Expert insights\n    - Practical examples\n    - Different viewpoints\n    - Source credibility assessment\n    \n    Target audience consideration: {targetAudience}\n    Script style: {scriptStyle}\n    \n    Provide comprehensive research that will help create an engaging, accurate, and informative video script.\n  `,\n    expectedOutput: `Detailed research report containing:\n    1. Key findings organized by category\n    2. Source credibility ratings\n    3. Most relevant and current information\n    4. Different perspectives on the topic\n    5. Practical examples and data points\n    6. Recommended focus areas for script writing`,\n    agent: webSearchAgent\n});\n// Enhanced research task with multiple search strategies\nconst advancedWebSearchTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    title: 'Multi-Strategy Topic Research',\n    description: `\n    Conduct advanced research on: {topic} using multiple search strategies.\n    \n    Search Strategy 1: Current Trends\n    - Search for \"{topic} 2024 trends\"\n    - Search for \"{topic} latest news\"\n    - Search for \"{topic} recent developments\"\n    \n    Search Strategy 2: Expert Insights\n    - Search for \"{topic} expert opinion\"\n    - Search for \"{topic} analysis\"\n    - Search for \"{topic} research study\"\n    \n    Search Strategy 3: Practical Information\n    - Search for \"{topic} how to\"\n    - Search for \"{topic} guide\"\n    - Search for \"{topic} examples\"\n    \n    Search Strategy 4: Audience-Specific\n    - Search for \"{topic} for {targetAudience}\"\n    - Search for \"{topic} beginner guide\" (if applicable)\n    \n    For each search, analyze:\n    1. Source authority and credibility\n    2. Information recency and relevance\n    3. Unique insights and perspectives\n    4. Practical applicability\n    5. Audience appropriateness\n    \n    Synthesize findings into:\n    - Core concepts and definitions\n    - Current state and trends\n    - Expert perspectives\n    - Practical examples\n    - Common questions and concerns\n    - Controversial or debated aspects\n    \n    Prioritize information that would make for engaging video content.\n  `,\n    expectedOutput: `Comprehensive research synthesis with:\n    1. Executive summary of key findings\n    2. Categorized information (trends, expert insights, practical info)\n    3. Source quality assessment\n    4. Content recommendations for video script\n    5. Potential hooks and engaging angles\n    6. Fact-check worthy claims and statistics`,\n    agent: webSearchAgent\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/ultron-kaiban/agents/web-search-agent.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/ultron-kaiban/agents/writer-agent.ts":
/*!*************************************************************!*\
  !*** ./src/lib/agents/ultron-kaiban/agents/writer-agent.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   enhancedScriptWritingTask: () => (/* binding */ enhancedScriptWritingTask),\n/* harmony export */   scriptWritingTask: () => (/* binding */ scriptWritingTask),\n/* harmony export */   writerAgent: () => (/* binding */ writerAgent)\n/* harmony export */ });\n/* harmony import */ var kaibanjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! kaibanjs */ \"(rsc)/./node_modules/kaibanjs/dist/bundle.mjs\");\n/**\n * Writer Agent for KaibanJS Ultron System\n * Generates YouTube scripts based on research and supervisor guidance\n */ \nconst writerAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'ScriptWriterAgent',\n    role: 'Professional YouTube Script Writer',\n    goal: 'Create engaging, well-structured YouTube scripts that captivate audiences and drive engagement based on research insights and supervisor guidance.',\n    background: `Expert YouTube script writer with deep understanding of:\n    - Viral content patterns and audience psychology\n    - Hook creation and attention retention techniques\n    - Narrative structure and pacing optimization\n    - Audience engagement and call-to-action placement\n    - Script formatting and delivery optimization\n    - Content adaptation for different audiences and styles\n    - SEO-friendly title and description creation\n    Specializes in creating scripts that sound natural, authentic, and engaging while incorporating proven success patterns.`,\n    tools: [],\n    maxIterations: 20\n});\nconst scriptWritingTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    title: 'YouTube Script Generation',\n    description: `\n    Create a compelling YouTube script for the topic: {topic}\n    \n    Based on the research findings and analysis provided, write a complete script that:\n    \n    SCRIPT REQUIREMENTS:\n    - Target Audience: {targetAudience}\n    - Script Style: {scriptStyle}\n    - Video Length: {videoLength} minutes\n    - Include Hooks: {includeHooks}\n    - Include Call-to-Action: {includeCallToAction}\n    \n    STRUCTURE TO FOLLOW:\n    \n    1. HOOK (0-15 seconds):\n    - Use research-backed hook strategies\n    - Create immediate curiosity or urgency\n    - Promise specific value or outcome\n    - Avoid generic openings\n    \n    2. INTRODUCTION (15-45 seconds):\n    - Brief personal introduction (if applicable)\n    - Clear video preview (what they'll learn)\n    - Build credibility and trust\n    - Set expectations for value delivery\n    \n    3. MAIN CONTENT (bulk of video):\n    - Organize into 3-5 key points maximum\n    - Use transitions that maintain engagement\n    - Include pattern interrupts every 60-90 seconds\n    - Incorporate storytelling elements\n    - Use examples and analogies for clarity\n    - Maintain conversational, engaging tone\n    \n    4. CONCLUSION (last 60-90 seconds):\n    - Summarize key takeaways\n    - Reinforce main value proposition\n    - Build momentum toward action\n    \n    5. CALL-TO-ACTION (if requested):\n    - Specific, clear action request\n    - Benefit-focused motivation\n    - Multiple engagement options\n    \n    WRITING GUIDELINES:\n    \n    Language and Tone:\n    - Write in second person (you/your) for direct connection\n    - Use active voice and strong verbs\n    - Keep sentences conversational length\n    - Include rhetorical questions for engagement\n    - Vary sentence structure for rhythm\n    \n    Engagement Techniques:\n    - Include viewer interaction prompts\n    - Use \"pattern interrupts\" to refresh attention\n    - Create open loops that get resolved later\n    - Reference common experiences and pain points\n    - Use specific numbers and concrete examples\n    \n    Content Flow:\n    - Ensure smooth transitions between sections\n    - Maintain logical progression of ideas\n    - Create emotional peaks and valleys\n    - Balance information with entertainment\n    - Include credibility indicators and proof points\n    \n    Technical Formatting:\n    - Indicate suggested pauses with [PAUSE]\n    - Mark emphasis points with [EMPHASIS]\n    - Note visual cue opportunities with [VISUAL]\n    - Include timing estimates for each section\n    \n    SPECIFIC REQUIREMENTS BASED ON RESEARCH:\n    - Incorporate successful hook patterns identified in analysis\n    - Use language patterns that resonate with target audience\n    - Apply psychological triggers found in successful videos\n    - Follow structure templates that show high engagement\n    - Avoid pitfalls identified in less successful content\n    \n    The script should feel natural when spoken aloud, maintain viewer interest throughout,\n    and achieve the specific goals outlined for this video topic and audience.\n  `,\n    expectedOutput: `Complete YouTube script with:\n    1. Compelling hook section (15-30 seconds)\n    2. Clear introduction with value preview (30-45 seconds)\n    3. Well-structured main content with engagement elements\n    4. Strong conclusion that reinforces value\n    5. Effective call-to-action (if requested)\n    6. Technical notes for delivery and production\n    7. Estimated timing for each section\n    8. SEO-friendly title suggestions (3-5 options)\n    9. Engaging description template\n    10. Key hashtags and tags for discoverability`,\n    agent: writerAgent\n});\nconst enhancedScriptWritingTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    title: 'Premium Script Creation with Advanced Techniques',\n    description: `\n    Create a premium YouTube script using advanced copywriting and psychological techniques.\n    \n    Topic: {topic}\n    Target Audience: {targetAudience}\n    Style: {scriptStyle}\n    \n    ADVANCED WRITING TECHNIQUES TO IMPLEMENT:\n    \n    1. PSYCHOLOGICAL HOOKS (Choose 2-3):\n    - Curiosity Gap: Start with incomplete information\n    - Pattern Interrupt: Break expected narrative flow\n    - Social Proof: Reference popularity or expert opinion\n    - Loss Aversion: What they might miss out on\n    - Authority: Establish expertise early\n    - Urgency: Time-sensitive information\n    - Relatability: Common experience connection\n    \n    2. COGNITIVE LOAD OPTIMIZATION:\n    - Chunk complex information into digestible pieces\n    - Use progressive disclosure (reveal information gradually)\n    - Employ the \"Rule of Three\" for key points\n    - Include mental breathing room between dense concepts\n    - Use analogies to simplify difficult concepts\n    \n    3. EMOTIONAL JOURNEY DESIGN:\n    - Map emotional arc: tension → resolution → satisfaction\n    - Include vulnerability moments for connection\n    - Create emotional peaks with surprising insights\n    - Use contrast to amplify key points\n    - End on a positive, empowering note\n    \n    4. ADVANCED ENGAGEMENT TECHNIQUES:\n    - Open loops: Tease information delivered later\n    - Callback references: Connect to earlier points\n    - Foreshadowing: Hint at upcoming valuable content\n    - Nested loops: Multiple curiosity threads\n    - Resolution rewards: Satisfy curiosity with valuable payoffs\n    \n    5. RETENTION OPTIMIZATION:\n    - Attention reset points every 60-90 seconds\n    - Value delivery confirmation (\"You just learned...\")\n    - Progress indicators (\"We're halfway through...\")\n    - Engagement checkpoints (\"Are you still with me?\")\n    - Preview upcoming value (\"In just a moment...\")\n    \n    6. CONVERSION PSYCHOLOGY:\n    - Build value before asking for action\n    - Reduce friction in call-to-action\n    - Create multiple commitment levels\n    - Use reciprocity principle\n    - Establish clear next steps\n    \n    7. AUTHENTICITY MARKERS:\n    - Personal anecdotes and experiences\n    - Admits limitations or mistakes\n    - Uses natural speech patterns\n    - Includes authentic reactions\n    - Shows genuine passion for topic\n    \n    SCRIPT SECTIONS WITH ADVANCED TECHNIQUES:\n    \n    MEGA-HOOK (First 10 seconds):\n    - Combine multiple psychological triggers\n    - Create maximum curiosity with minimum information\n    - Use pattern interrupt to grab attention\n    - Promise transformation or revelation\n    \n    EXPANDED INTRODUCTION (30-60 seconds):\n    - Build authority with credentials or results\n    - Create connection through shared experience\n    - Preview specific value with concrete outcomes\n    - Set up the emotional journey ahead\n    \n    VALUE-PACKED MAIN CONTENT:\n    - Each section includes \"aha moments\"\n    - Use story-driven explanations\n    - Include interactive elements\n    - Provide immediately applicable insights\n    - Connect each point to bigger picture\n    \n    POWER CONCLUSION:\n    - Reinforce transformation achieved\n    - Connect ending back to opening hook\n    - Inspire action with emotional appeal\n    - Leave them wanting more content\n    \n    OPTIMIZED CALL-TO-ACTION:\n    - Build on value already delivered\n    - Offer multiple engagement options\n    - Use urgency and scarcity appropriately\n    - Make action feel like natural next step\n    \n    Include specific implementation notes for each advanced technique used.\n  `,\n    expectedOutput: `Premium YouTube script featuring:\n    1. Psychologically optimized mega-hook\n    2. Authority-building introduction with emotional connection\n    3. Value-dense main content with engagement optimization\n    4. Transformation-focused conclusion\n    5. Multi-layered call-to-action strategy\n    6. Advanced technique implementation notes\n    7. Emotional journey mapping\n    8. Retention optimization markers\n    9. Conversion psychology elements\n    10. Multiple title/description variations optimized for different psychological triggers`,\n    agent: writerAgent\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/ultron-kaiban/agents/writer-agent.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/ultron-kaiban/ultron-team.ts":
/*!*****************************************************!*\
  !*** ./src/lib/agents/ultron-kaiban/ultron-team.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   advancedUltronTeam: () => (/* binding */ advancedUltronTeam),\n/* harmony export */   generateMultipleScripts: () => (/* binding */ generateMultipleScripts),\n/* harmony export */   generateScript: () => (/* binding */ generateScript),\n/* harmony export */   getTeamInfo: () => (/* binding */ getTeamInfo),\n/* harmony export */   quickScriptTeam: () => (/* binding */ quickScriptTeam),\n/* harmony export */   ultronTeam: () => (/* binding */ ultronTeam)\n/* harmony export */ });\n/* harmony import */ var kaibanjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! kaibanjs */ \"(rsc)/./node_modules/kaibanjs/dist/bundle.mjs\");\n/* harmony import */ var _agents_web_search_agent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./agents/web-search-agent */ \"(rsc)/./src/lib/agents/ultron-kaiban/agents/web-search-agent.ts\");\n/* harmony import */ var _agents_caption_analyser_agent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./agents/caption-analyser-agent */ \"(rsc)/./src/lib/agents/ultron-kaiban/agents/caption-analyser-agent.ts\");\n/* harmony import */ var _agents_writer_agent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./agents/writer-agent */ \"(rsc)/./src/lib/agents/ultron-kaiban/agents/writer-agent.ts\");\n/* harmony import */ var _agents_supervisor_agent__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./agents/supervisor-agent */ \"(rsc)/./src/lib/agents/ultron-kaiban/agents/supervisor-agent.ts\");\n/**\n * KaibanJS Ultron Team Configuration\n * Orchestrates the complete YouTube script generation workflow\n */ \n\n\n\n\n/**\n * Standard Ultron Team for YouTube Script Generation\n * Workflow: Research → Analysis → Supervision → Writing → Quality Assurance\n */ const ultronTeam = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Team({\n    name: 'Ultron YouTube Script Generation Team',\n    agents: [\n        _agents_web_search_agent__WEBPACK_IMPORTED_MODULE_1__.webSearchAgent,\n        _agents_caption_analyser_agent__WEBPACK_IMPORTED_MODULE_2__.captionAnalyserAgent,\n        _agents_supervisor_agent__WEBPACK_IMPORTED_MODULE_4__.supervisorAgent,\n        _agents_writer_agent__WEBPACK_IMPORTED_MODULE_3__.writerAgent\n    ],\n    tasks: [\n        _agents_web_search_agent__WEBPACK_IMPORTED_MODULE_1__.webSearchTask,\n        _agents_caption_analyser_agent__WEBPACK_IMPORTED_MODULE_2__.captionAnalysisTask,\n        _agents_supervisor_agent__WEBPACK_IMPORTED_MODULE_4__.supervisorCoordinationTask,\n        _agents_writer_agent__WEBPACK_IMPORTED_MODULE_3__.scriptWritingTask,\n        _agents_supervisor_agent__WEBPACK_IMPORTED_MODULE_4__.qualityAssuranceTask\n    ],\n    inputs: {\n        topic: '',\n        targetAudience: 'General YouTube viewers',\n        scriptStyle: 'educational',\n        videoLength: 10,\n        includeHooks: true,\n        includeCallToAction: true\n    },\n    env: {\n        GEMINI_API_KEY: \"AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY\" || 0,\n        TAVILY_API_KEY: process.env.TAVILY_API_KEY || '',\n        OPENAI_API_KEY: process.env.OPENAI_API_KEY || ''\n    }\n});\n/**\n * Advanced Ultron Team with Enhanced Capabilities\n * Uses advanced tasks for more sophisticated analysis and writing\n */ const advancedUltronTeam = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Team({\n    name: 'Advanced Ultron YouTube Script Generation Team',\n    agents: [\n        _agents_web_search_agent__WEBPACK_IMPORTED_MODULE_1__.webSearchAgent,\n        _agents_caption_analyser_agent__WEBPACK_IMPORTED_MODULE_2__.captionAnalyserAgent,\n        _agents_supervisor_agent__WEBPACK_IMPORTED_MODULE_4__.supervisorAgent,\n        _agents_writer_agent__WEBPACK_IMPORTED_MODULE_3__.writerAgent\n    ],\n    tasks: [\n        _agents_web_search_agent__WEBPACK_IMPORTED_MODULE_1__.advancedWebSearchTask,\n        _agents_caption_analyser_agent__WEBPACK_IMPORTED_MODULE_2__.advancedCaptionAnalysisTask,\n        _agents_supervisor_agent__WEBPACK_IMPORTED_MODULE_4__.supervisorCoordinationTask,\n        _agents_writer_agent__WEBPACK_IMPORTED_MODULE_3__.enhancedScriptWritingTask,\n        _agents_supervisor_agent__WEBPACK_IMPORTED_MODULE_4__.qualityAssuranceTask\n    ],\n    inputs: {\n        topic: '',\n        targetAudience: 'General YouTube viewers',\n        scriptStyle: 'educational',\n        videoLength: 10,\n        includeHooks: true,\n        includeCallToAction: true\n    },\n    env: {\n        GEMINI_API_KEY: \"AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY\" || 0,\n        TAVILY_API_KEY: process.env.TAVILY_API_KEY || '',\n        OPENAI_API_KEY: process.env.OPENAI_API_KEY || ''\n    }\n});\n/**\n * Quick Script Team for Rapid Content Creation\n * Streamlined workflow for faster turnaround\n */ const quickScriptTeam = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Team({\n    name: 'Quick Script Generation Team',\n    agents: [\n        _agents_web_search_agent__WEBPACK_IMPORTED_MODULE_1__.webSearchAgent,\n        _agents_writer_agent__WEBPACK_IMPORTED_MODULE_3__.writerAgent\n    ],\n    tasks: [\n        _agents_web_search_agent__WEBPACK_IMPORTED_MODULE_1__.webSearchTask,\n        _agents_writer_agent__WEBPACK_IMPORTED_MODULE_3__.scriptWritingTask\n    ],\n    inputs: {\n        topic: '',\n        targetAudience: 'General YouTube viewers',\n        scriptStyle: 'educational',\n        videoLength: 5,\n        includeHooks: true,\n        includeCallToAction: false\n    },\n    env: {\n        GEMINI_API_KEY: \"AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY\" || 0,\n        TAVILY_API_KEY: process.env.TAVILY_API_KEY || '',\n        OPENAI_API_KEY: process.env.OPENAI_API_KEY || ''\n    }\n});\n/**\n * Simple function to start a team with given config\n */ async function generateScript(config) {\n    try {\n        let team;\n        // Select team based on type\n        switch(config.teamType){\n            case 'advanced':\n                team = advancedUltronTeam;\n                break;\n            case 'quick':\n                team = quickScriptTeam;\n                break;\n            default:\n                team = ultronTeam;\n        }\n        console.log(`🚀 Starting ${config.teamType || 'standard'} Ultron team for topic: ${config.topic}`);\n        // Start the team workflow with inputs\n        const result = await team.start({\n            topic: config.topic,\n            targetAudience: config.targetAudience || 'General YouTube viewers',\n            scriptStyle: config.scriptStyle || 'educational',\n            videoLength: config.videoLength || 10,\n            includeHooks: config.includeHooks ?? true,\n            includeCallToAction: config.includeCallToAction ?? true\n        });\n        if (result && result.result) {\n            console.log('✅ Script generation completed successfully');\n            return {\n                success: true,\n                script: result.result,\n                metadata: {\n                    teamType: config.teamType || 'standard',\n                    topic: config.topic,\n                    targetAudience: config.targetAudience,\n                    scriptStyle: config.scriptStyle,\n                    videoLength: config.videoLength,\n                    timestamp: new Date().toISOString()\n                }\n            };\n        } else {\n            throw new Error('No result generated from team workflow');\n        }\n    } catch (error) {\n        console.error('❌ Script generation failed:', error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : 'Unknown error occurred'\n        };\n    }\n}\n/**\n * Batch script generation for multiple topics\n */ async function generateMultipleScripts(topics, baseConfig) {\n    const results = [];\n    for (const topic of topics){\n        console.log(`\\n📝 Processing topic: ${topic}`);\n        const result = await generateScript({\n            ...baseConfig,\n            topic\n        });\n        results.push({\n            topic,\n            ...result\n        });\n        // Add delay between requests to avoid rate limits\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n    }\n    return results;\n}\n/**\n * Get team information\n */ function getTeamInfo(teamType = 'standard') {\n    const teams = {\n        standard: {\n            name: 'Ultron YouTube Script Generation Team',\n            agents: [\n                'ResearcherAgent',\n                'CaptionAnalyserAgent',\n                'SupervisorAgent',\n                'ScriptWriterAgent'\n            ],\n            tasks: [\n                'Web Search',\n                'Caption Analysis',\n                'Supervision',\n                'Script Writing',\n                'Quality Assurance'\n            ]\n        },\n        advanced: {\n            name: 'Advanced Ultron YouTube Script Generation Team',\n            agents: [\n                'ResearcherAgent',\n                'CaptionAnalyserAgent',\n                'SupervisorAgent',\n                'ScriptWriterAgent'\n            ],\n            tasks: [\n                'Advanced Web Search',\n                'Advanced Caption Analysis',\n                'Supervision',\n                'Enhanced Script Writing',\n                'Quality Assurance'\n            ]\n        },\n        quick: {\n            name: 'Quick Script Generation Team',\n            agents: [\n                'ResearcherAgent',\n                'ScriptWriterAgent'\n            ],\n            tasks: [\n                'Web Search',\n                'Script Writing'\n            ]\n        }\n    };\n    return teams[teamType];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/ultron-kaiban/ultron-team.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/kaibanjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fultron%2Fkaiban%2Froute&page=%2Fapi%2Fultron%2Fkaiban%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fultron%2Fkaiban%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();