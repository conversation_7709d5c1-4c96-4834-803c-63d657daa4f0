"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_VideoAlchemyPreview_tsx"],{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/globe.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Globe)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.356.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Globe = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Globe\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20\",\n            key: \"13o1zl\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M2 12h20\",\n            key: \"9i4pu4\"\n        }\n    ]\n]);\n //# sourceMappingURL=globe.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvZ2xvYmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFhTSxjQUFRLGdFQUFnQixDQUFDLE9BQVM7SUFDdEM7UUFBQyxRQUFVO1FBQUE7WUFBRSxFQUFJO1lBQU0sQ0FBSSxRQUFNO1lBQUEsQ0FBRztZQUFNLEdBQUs7UUFBQSxDQUFVO0tBQUE7SUFDekQ7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLENBQW1EO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtJQUNoRjtRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBWTtZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7Q0FDMUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zcmMvaWNvbnMvZ2xvYmUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBHbG9iZVxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4WTJseVkyeGxJR040UFNJeE1pSWdZM2s5SWpFeUlpQnlQU0l4TUNJZ0x6NEtJQ0E4Y0dGMGFDQmtQU0pOTVRJZ01tRXhOQzQxSURFMExqVWdNQ0F3SURBZ01DQXlNQ0F4TkM0MUlERTBMalVnTUNBd0lEQWdNQzB5TUNJZ0x6NEtJQ0E4Y0dGMGFDQmtQU0pOTWlBeE1tZ3lNQ0lnTHo0S1BDOXpkbWMrQ2c9PSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvZ2xvYmVcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBHbG9iZSA9IGNyZWF0ZUx1Y2lkZUljb24oJ0dsb2JlJywgW1xuICBbJ2NpcmNsZScsIHsgY3g6ICcxMicsIGN5OiAnMTInLCByOiAnMTAnLCBrZXk6ICcxbWdsYXknIH1dLFxuICBbJ3BhdGgnLCB7IGQ6ICdNMTIgMmExNC41IDE0LjUgMCAwIDAgMCAyMCAxNC41IDE0LjUgMCAwIDAgMC0yMCcsIGtleTogJzEzbzF6bCcgfV0sXG4gIFsncGF0aCcsIHsgZDogJ00yIDEyaDIwJywga2V5OiAnOWk0cHU0JyB9XSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBHbG9iZTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/languages.js":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/languages.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Languages)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.356.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Languages = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Languages\", [\n    [\n        \"path\",\n        {\n            d: \"m5 8 6 6\",\n            key: \"1wu5hv\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m4 14 6-6 2-3\",\n            key: \"1k1g8d\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M2 5h12\",\n            key: \"or177f\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M7 2h1\",\n            key: \"1t2jsx\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m22 22-5-10-5 10\",\n            key: \"don7ne\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 18h6\",\n            key: \"1m8k6r\"\n        }\n    ]\n]);\n //# sourceMappingURL=languages.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/languages.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/youtube.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/youtube.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Youtube)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.356.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Youtube = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Youtube\", [\n    [\n        \"path\",\n        {\n            d: \"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17\",\n            key: \"1q2vi4\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m10 15 5-3-5-3z\",\n            key: \"1jp15x\"\n        }\n    ]\n]);\n //# sourceMappingURL=youtube.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/youtube.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/VideoAlchemyPreview.tsx":
/*!************************************************!*\
  !*** ./src/components/VideoAlchemyPreview.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VideoAlchemyPreview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_FileText_Globe_Languages_Sparkles_Video_Wand2_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Globe,Languages,Sparkles,Video,Wand2,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Globe_Languages_Sparkles_Video_Wand2_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Globe,Languages,Sparkles,Video,Wand2,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Globe_Languages_Sparkles_Video_Wand2_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Globe,Languages,Sparkles,Video,Wand2,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Globe_Languages_Sparkles_Video_Wand2_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Globe,Languages,Sparkles,Video,Wand2,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Globe_Languages_Sparkles_Video_Wand2_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Globe,Languages,Sparkles,Video,Wand2,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Globe_Languages_Sparkles_Video_Wand2_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Globe,Languages,Sparkles,Video,Wand2,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/languages.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Globe_Languages_Sparkles_Video_Wand2_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Globe,Languages,Sparkles,Video,Wand2,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction VideoAlchemyPreview() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full h-full flex items-center justify-center overflow-hidden bg-gradient-to-br from-purple-950/20 via-black to-pink-950/20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        animate: {\n                            scale: [\n                                1,\n                                1.2,\n                                1\n                            ],\n                            opacity: [\n                                0.3,\n                                0.5,\n                                0.3\n                            ]\n                        },\n                        transition: {\n                            duration: 4,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute top-1/4 left-1/4 w-48 h-48 bg-purple-600/30 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        animate: {\n                            scale: [\n                                1,\n                                1.3,\n                                1\n                            ],\n                            opacity: [\n                                0.3,\n                                0.6,\n                                0.3\n                            ]\n                        },\n                        transition: {\n                            duration: 5,\n                            repeat: Infinity,\n                            ease: \"easeInOut\",\n                            delay: 1\n                        },\n                        className: \"absolute bottom-1/4 right-1/4 w-56 h-56 bg-pink-600/30 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                initial: {\n                                    scale: 0,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    scale: 1,\n                                    opacity: 1\n                                },\n                                transition: {\n                                    delay: 0.2,\n                                    duration: 0.5\n                                },\n                                className: \"absolute -left-20 top-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                            animate: {\n                                                scale: [\n                                                    1,\n                                                    1.1,\n                                                    1\n                                                ]\n                                            },\n                                            transition: {\n                                                duration: 2,\n                                                repeat: Infinity\n                                            },\n                                            className: \"absolute inset-0 bg-red-500/20 rounded-xl blur-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-gradient-to-r from-red-600 to-pink-600 p-3 rounded-xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Globe_Languages_Sparkles_Video_Wand2_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-8 h-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                initial: {\n                                    scale: 0,\n                                    rotate: -180\n                                },\n                                animate: {\n                                    scale: 1,\n                                    rotate: 0\n                                },\n                                transition: {\n                                    delay: 0.5,\n                                    duration: 0.8,\n                                    type: \"spring\"\n                                },\n                                className: \"relative z-20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                    animate: {\n                                        rotate: [\n                                            0,\n                                            15,\n                                            -15,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 4,\n                                        repeat: Infinity,\n                                        ease: \"easeInOut\"\n                                    },\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                            animate: {\n                                                scale: [\n                                                    1,\n                                                    1.2,\n                                                    1\n                                                ]\n                                            },\n                                            transition: {\n                                                duration: 2,\n                                                repeat: Infinity\n                                            },\n                                            className: \"absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full blur-2xl opacity-60\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-gradient-to-r from-purple-600 to-pink-600 p-4 rounded-full shadow-2xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Globe_Languages_Sparkles_Video_Wand2_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-10 h-10 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                initial: {\n                                    scale: 0,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    scale: 1,\n                                    opacity: 1\n                                },\n                                transition: {\n                                    delay: 0.8,\n                                    duration: 0.5\n                                },\n                                className: \"absolute -right-20 top-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                            animate: {\n                                                scale: [\n                                                    1,\n                                                    1.1,\n                                                    1\n                                                ]\n                                            },\n                                            transition: {\n                                                duration: 2,\n                                                repeat: Infinity,\n                                                delay: 0.5\n                                            },\n                                            className: \"absolute inset-0 bg-blue-500/20 rounded-xl blur-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-gradient-to-r from-blue-600 to-purple-600 p-3 rounded-xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Globe_Languages_Sparkles_Video_Wand2_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-8 h-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 1\n                        },\n                        className: \"absolute inset-0 pointer-events-none\",\n                        children: [\n                            ...Array(6)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                initial: {\n                                    scale: 0,\n                                    x: 0,\n                                    y: 0\n                                },\n                                animate: {\n                                    scale: [\n                                        0,\n                                        1,\n                                        0\n                                    ],\n                                    x: [\n                                        0,\n                                        (i % 2 ? 40 : -40) * (i + 1) / 2\n                                    ],\n                                    y: [\n                                        0,\n                                        -20 - i * 10\n                                    ]\n                                },\n                                transition: {\n                                    duration: 2,\n                                    delay: 1.2 + i * 0.2,\n                                    repeat: Infinity,\n                                    repeatDelay: 3\n                                },\n                                className: \"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Globe_Languages_Sparkles_Video_Wand2_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4 text-purple-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this)\n                            }, i, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 1.5\n                        },\n                        className: \"flex items-center space-x-4 mt-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                animate: {\n                                    y: [\n                                        0,\n                                        -5,\n                                        0\n                                    ]\n                                },\n                                transition: {\n                                    duration: 2,\n                                    repeat: Infinity,\n                                    delay: 0\n                                },\n                                className: \"bg-white/10 backdrop-blur-sm rounded-lg p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Globe_Languages_Sparkles_Video_Wand2_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5 text-blue-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                animate: {\n                                    y: [\n                                        0,\n                                        -5,\n                                        0\n                                    ]\n                                },\n                                transition: {\n                                    duration: 2,\n                                    repeat: Infinity,\n                                    delay: 0.3\n                                },\n                                className: \"bg-white/10 backdrop-blur-sm rounded-lg p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Globe_Languages_Sparkles_Video_Wand2_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-5 h-5 text-purple-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                animate: {\n                                    y: [\n                                        0,\n                                        -5,\n                                        0\n                                    ]\n                                },\n                                transition: {\n                                    duration: 2,\n                                    repeat: Infinity,\n                                    delay: 0.6\n                                },\n                                className: \"bg-white/10 backdrop-blur-sm rounded-lg p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Globe_Languages_Sparkles_Video_Wand2_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5 text-pink-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 2\n                        },\n                        className: \"mt-6 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-400\",\n                            children: \"Transform Videos → SEO Articles\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/VideoAlchemyPreview.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n_c = VideoAlchemyPreview;\nvar _c;\n$RefreshReg$(_c, \"VideoAlchemyPreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1ZpZGVvQWxjaGVteVByZXZpZXcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBRXVDO0FBQ29EO0FBRTVFLFNBQVNRO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDVixpREFBTUEsQ0FBQ1MsR0FBRzt3QkFDVEUsU0FBUzs0QkFDUEMsT0FBTztnQ0FBQztnQ0FBRztnQ0FBSzs2QkFBRTs0QkFDbEJDLFNBQVM7Z0NBQUM7Z0NBQUs7Z0NBQUs7NkJBQUk7d0JBQzFCO3dCQUNBQyxZQUFZOzRCQUNWQyxVQUFVOzRCQUNWQyxRQUFRQzs0QkFDUkMsTUFBTTt3QkFDUjt3QkFDQVIsV0FBVTs7Ozs7O2tDQUVaLDhEQUFDVixpREFBTUEsQ0FBQ1MsR0FBRzt3QkFDVEUsU0FBUzs0QkFDUEMsT0FBTztnQ0FBQztnQ0FBRztnQ0FBSzs2QkFBRTs0QkFDbEJDLFNBQVM7Z0NBQUM7Z0NBQUs7Z0NBQUs7NkJBQUk7d0JBQzFCO3dCQUNBQyxZQUFZOzRCQUNWQyxVQUFVOzRCQUNWQyxRQUFRQzs0QkFDUkMsTUFBTTs0QkFDTkMsT0FBTzt3QkFDVDt3QkFDQVQsV0FBVTs7Ozs7Ozs7Ozs7OzBCQUtkLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FFYiw4REFBQ1YsaURBQU1BLENBQUNTLEdBQUc7Z0NBQ1RXLFNBQVM7b0NBQUVSLE9BQU87b0NBQUdDLFNBQVM7Z0NBQUU7Z0NBQ2hDRixTQUFTO29DQUFFQyxPQUFPO29DQUFHQyxTQUFTO2dDQUFFO2dDQUNoQ0MsWUFBWTtvQ0FBRUssT0FBTztvQ0FBS0osVUFBVTtnQ0FBSTtnQ0FDeENMLFdBQVU7MENBRVYsNEVBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ1YsaURBQU1BLENBQUNTLEdBQUc7NENBQ1RFLFNBQVM7Z0RBQUVDLE9BQU87b0RBQUM7b0RBQUc7b0RBQUs7aURBQUU7NENBQUM7NENBQzlCRSxZQUFZO2dEQUFFQyxVQUFVO2dEQUFHQyxRQUFRQzs0Q0FBUzs0Q0FDNUNQLFdBQVU7Ozs7OztzREFFWiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNMLGlJQUFPQTtnREFBQ0ssV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FNekIsOERBQUNWLGlEQUFNQSxDQUFDUyxHQUFHO2dDQUNUVyxTQUFTO29DQUFFUixPQUFPO29DQUFHUyxRQUFRLENBQUM7Z0NBQUk7Z0NBQ2xDVixTQUFTO29DQUFFQyxPQUFPO29DQUFHUyxRQUFRO2dDQUFFO2dDQUMvQlAsWUFBWTtvQ0FBRUssT0FBTztvQ0FBS0osVUFBVTtvQ0FBS08sTUFBTTtnQ0FBUztnQ0FDeERaLFdBQVU7MENBRVYsNEVBQUNWLGlEQUFNQSxDQUFDUyxHQUFHO29DQUNURSxTQUFTO3dDQUFFVSxRQUFROzRDQUFDOzRDQUFHOzRDQUFJLENBQUM7NENBQUk7eUNBQUU7b0NBQUM7b0NBQ25DUCxZQUFZO3dDQUFFQyxVQUFVO3dDQUFHQyxRQUFRQzt3Q0FBVUMsTUFBTTtvQ0FBWTtvQ0FDL0RSLFdBQVU7O3NEQUVWLDhEQUFDVixpREFBTUEsQ0FBQ1MsR0FBRzs0Q0FDVEUsU0FBUztnREFBRUMsT0FBTztvREFBQztvREFBRztvREFBSztpREFBRTs0Q0FBQzs0Q0FDOUJFLFlBQVk7Z0RBQUVDLFVBQVU7Z0RBQUdDLFFBQVFDOzRDQUFTOzRDQUM1Q1AsV0FBVTs7Ozs7O3NEQUVaLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ1IsaUlBQUtBO2dEQUFDUSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU12Qiw4REFBQ1YsaURBQU1BLENBQUNTLEdBQUc7Z0NBQ1RXLFNBQVM7b0NBQUVSLE9BQU87b0NBQUdDLFNBQVM7Z0NBQUU7Z0NBQ2hDRixTQUFTO29DQUFFQyxPQUFPO29DQUFHQyxTQUFTO2dDQUFFO2dDQUNoQ0MsWUFBWTtvQ0FBRUssT0FBTztvQ0FBS0osVUFBVTtnQ0FBSTtnQ0FDeENMLFdBQVU7MENBRVYsNEVBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ1YsaURBQU1BLENBQUNTLEdBQUc7NENBQ1RFLFNBQVM7Z0RBQUVDLE9BQU87b0RBQUM7b0RBQUc7b0RBQUs7aURBQUU7NENBQUM7NENBQzlCRSxZQUFZO2dEQUFFQyxVQUFVO2dEQUFHQyxRQUFRQztnREFBVUUsT0FBTzs0Q0FBSTs0Q0FDeERULFdBQVU7Ozs7OztzREFFWiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNQLGlJQUFRQTtnREFBQ08sV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPNUIsOERBQUNWLGlEQUFNQSxDQUFDUyxHQUFHO3dCQUNUVyxTQUFTOzRCQUFFUCxTQUFTO3dCQUFFO3dCQUN0QkYsU0FBUzs0QkFBRUUsU0FBUzt3QkFBRTt3QkFDdEJDLFlBQVk7NEJBQUVLLE9BQU87d0JBQUU7d0JBQ3ZCVCxXQUFVO2tDQUVUOytCQUFJYSxNQUFNO3lCQUFHLENBQUNDLEdBQUcsQ0FBQyxDQUFDQyxHQUFHQyxrQkFDckIsOERBQUMxQixpREFBTUEsQ0FBQ1MsR0FBRztnQ0FFVFcsU0FBUztvQ0FBRVIsT0FBTztvQ0FBR2UsR0FBRztvQ0FBR0MsR0FBRztnQ0FBRTtnQ0FDaENqQixTQUFTO29DQUNQQyxPQUFPO3dDQUFDO3dDQUFHO3dDQUFHO3FDQUFFO29DQUNoQmUsR0FBRzt3Q0FBQzt3Q0FBSUQsQ0FBQUEsSUFBSSxJQUFJLEtBQUssQ0FBQyxFQUFDLElBQU1BLENBQUFBLElBQUksS0FBSztxQ0FBRTtvQ0FDeENFLEdBQUc7d0NBQUM7d0NBQUcsQ0FBQyxLQUFNRixJQUFJO3FDQUFJO2dDQUN4QjtnQ0FDQVosWUFBWTtvQ0FDVkMsVUFBVTtvQ0FDVkksT0FBTyxNQUFPTyxJQUFJO29DQUNsQlYsUUFBUUM7b0NBQ1JZLGFBQWE7Z0NBQ2Y7Z0NBQ0FuQixXQUFVOzBDQUVWLDRFQUFDTixpSUFBUUE7b0NBQUNNLFdBQVU7Ozs7OzsrQkFmZmdCOzs7Ozs7Ozs7O2tDQXFCWCw4REFBQzFCLGlEQUFNQSxDQUFDUyxHQUFHO3dCQUNUVyxTQUFTOzRCQUFFUCxTQUFTOzRCQUFHZSxHQUFHO3dCQUFHO3dCQUM3QmpCLFNBQVM7NEJBQUVFLFNBQVM7NEJBQUdlLEdBQUc7d0JBQUU7d0JBQzVCZCxZQUFZOzRCQUFFSyxPQUFPO3dCQUFJO3dCQUN6QlQsV0FBVTs7MENBRVYsOERBQUNWLGlEQUFNQSxDQUFDUyxHQUFHO2dDQUNURSxTQUFTO29DQUFFaUIsR0FBRzt3Q0FBQzt3Q0FBRyxDQUFDO3dDQUFHO3FDQUFFO2dDQUFDO2dDQUN6QmQsWUFBWTtvQ0FBRUMsVUFBVTtvQ0FBR0MsUUFBUUM7b0NBQVVFLE9BQU87Z0NBQUU7Z0NBQ3REVCxXQUFVOzBDQUVWLDRFQUFDSixpSUFBS0E7b0NBQUNJLFdBQVU7Ozs7Ozs7Ozs7OzBDQUVuQiw4REFBQ1YsaURBQU1BLENBQUNTLEdBQUc7Z0NBQ1RFLFNBQVM7b0NBQUVpQixHQUFHO3dDQUFDO3dDQUFHLENBQUM7d0NBQUc7cUNBQUU7Z0NBQUM7Z0NBQ3pCZCxZQUFZO29DQUFFQyxVQUFVO29DQUFHQyxRQUFRQztvQ0FBVUUsT0FBTztnQ0FBSTtnQ0FDeERULFdBQVU7MENBRVYsNEVBQUNILGlJQUFTQTtvQ0FBQ0csV0FBVTs7Ozs7Ozs7Ozs7MENBRXZCLDhEQUFDVixpREFBTUEsQ0FBQ1MsR0FBRztnQ0FDVEUsU0FBUztvQ0FBRWlCLEdBQUc7d0NBQUM7d0NBQUcsQ0FBQzt3Q0FBRztxQ0FBRTtnQ0FBQztnQ0FDekJkLFlBQVk7b0NBQUVDLFVBQVU7b0NBQUdDLFFBQVFDO29DQUFVRSxPQUFPO2dDQUFJO2dDQUN4RFQsV0FBVTswQ0FFViw0RUFBQ1QsaUlBQUtBO29DQUFDUyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLckIsOERBQUNWLGlEQUFNQSxDQUFDUyxHQUFHO3dCQUNUVyxTQUFTOzRCQUFFUCxTQUFTO3dCQUFFO3dCQUN0QkYsU0FBUzs0QkFBRUUsU0FBUzt3QkFBRTt3QkFDdEJDLFlBQVk7NEJBQUVLLE9BQU87d0JBQUU7d0JBQ3ZCVCxXQUFVO2tDQUVWLDRFQUFDb0I7NEJBQUVwQixXQUFVO3NDQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLL0M7S0F6S3dCRiIsInNvdXJjZXMiOlsiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL3NyYy9jb21wb25lbnRzL1ZpZGVvQWxjaGVteVByZXZpZXcudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSAnZnJhbWVyLW1vdGlvbic7XG5pbXBvcnQgeyBWaWRlbywgV2FuZDIsIEZpbGVUZXh0LCBTcGFya2xlcywgWW91dHViZSwgR2xvYmUsIExhbmd1YWdlcyB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFZpZGVvQWxjaGVteVByZXZpZXcoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LWZ1bGwgaC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG92ZXJmbG93LWhpZGRlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLXB1cnBsZS05NTAvMjAgdmlhLWJsYWNrIHRvLXBpbmstOTUwLzIwXCI+XG4gICAgICB7LyogQW5pbWF0ZWQgQmFja2dyb3VuZCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMFwiPlxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIGFuaW1hdGU9e3tcbiAgICAgICAgICAgIHNjYWxlOiBbMSwgMS4yLCAxXSxcbiAgICAgICAgICAgIG9wYWNpdHk6IFswLjMsIDAuNSwgMC4zXSxcbiAgICAgICAgICB9fVxuICAgICAgICAgIHRyYW5zaXRpb249e3tcbiAgICAgICAgICAgIGR1cmF0aW9uOiA0LFxuICAgICAgICAgICAgcmVwZWF0OiBJbmZpbml0eSxcbiAgICAgICAgICAgIGVhc2U6IFwiZWFzZUluT3V0XCJcbiAgICAgICAgICB9fVxuICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0xLzQgbGVmdC0xLzQgdy00OCBoLTQ4IGJnLXB1cnBsZS02MDAvMzAgcm91bmRlZC1mdWxsIGJsdXItM3hsXCJcbiAgICAgICAgLz5cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBhbmltYXRlPXt7XG4gICAgICAgICAgICBzY2FsZTogWzEsIDEuMywgMV0sXG4gICAgICAgICAgICBvcGFjaXR5OiBbMC4zLCAwLjYsIDAuM10sXG4gICAgICAgICAgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7XG4gICAgICAgICAgICBkdXJhdGlvbjogNSxcbiAgICAgICAgICAgIHJlcGVhdDogSW5maW5pdHksXG4gICAgICAgICAgICBlYXNlOiBcImVhc2VJbk91dFwiLFxuICAgICAgICAgICAgZGVsYXk6IDFcbiAgICAgICAgICB9fVxuICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0xLzQgcmlnaHQtMS80IHctNTYgaC01NiBiZy1waW5rLTYwMC8zMCByb3VuZGVkLWZ1bGwgYmx1ci0zeGxcIlxuICAgICAgICAvPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBNYWluIEFuaW1hdGlvbiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMCBmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICB7LyogVmlkZW8gdG8gQXJ0aWNsZSBGbG93ICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgey8qIFZpZGVvIEljb24gKi99XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e3sgc2NhbGU6IDAsIG9wYWNpdHk6IDAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3sgc2NhbGU6IDEsIG9wYWNpdHk6IDEgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IDAuMiwgZHVyYXRpb246IDAuNSB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLWxlZnQtMjAgdG9wLTBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICBhbmltYXRlPXt7IHNjYWxlOiBbMSwgMS4xLCAxXSB9fVxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDIsIHJlcGVhdDogSW5maW5pdHkgfX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLXJlZC01MDAvMjAgcm91bmRlZC14bCBibHVyLXhsXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBiZy1ncmFkaWVudC10by1yIGZyb20tcmVkLTYwMCB0by1waW5rLTYwMCBwLTMgcm91bmRlZC14bFwiPlxuICAgICAgICAgICAgICAgIDxZb3V0dWJlIGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuXG4gICAgICAgICAgey8qIE1hZ2ljIFdhbmQgaW4gQ2VudGVyICovfVxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBpbml0aWFsPXt7IHNjYWxlOiAwLCByb3RhdGU6IC0xODAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3sgc2NhbGU6IDEsIHJvdGF0ZTogMCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogMC41LCBkdXJhdGlvbjogMC44LCB0eXBlOiBcInNwcmluZ1wiIH19XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZSB6LTIwXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICBhbmltYXRlPXt7IHJvdGF0ZTogWzAsIDE1LCAtMTUsIDBdIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDQsIHJlcGVhdDogSW5maW5pdHksIGVhc2U6IFwiZWFzZUluT3V0XCIgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmVcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgc2NhbGU6IFsxLCAxLjIsIDFdIH19XG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMiwgcmVwZWF0OiBJbmZpbml0eSB9fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tciBmcm9tLXB1cnBsZS02MDAgdG8tcGluay02MDAgcm91bmRlZC1mdWxsIGJsdXItMnhsIG9wYWNpdHktNjBcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1wdXJwbGUtNjAwIHRvLXBpbmstNjAwIHAtNCByb3VuZGVkLWZ1bGwgc2hhZG93LTJ4bFwiPlxuICAgICAgICAgICAgICAgIDxXYW5kMiBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICAgIHsvKiBBcnRpY2xlIEljb24gKi99XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e3sgc2NhbGU6IDAsIG9wYWNpdHk6IDAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3sgc2NhbGU6IDEsIG9wYWNpdHk6IDEgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IDAuOCwgZHVyYXRpb246IDAuNSB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXJpZ2h0LTIwIHRvcC0wXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBzY2FsZTogWzEsIDEuMSwgMV0gfX1cbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAyLCByZXBlYXQ6IEluZmluaXR5LCBkZWxheTogMC41IH19XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ibHVlLTUwMC8yMCByb3VuZGVkLXhsIGJsdXIteGxcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMCB0by1wdXJwbGUtNjAwIHAtMyByb3VuZGVkLXhsXCI+XG4gICAgICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogU3BhcmtsZXMgQW5pbWF0aW9uICovfVxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCB9fVxuICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSB9fVxuICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IDEgfX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIHBvaW50ZXItZXZlbnRzLW5vbmVcIlxuICAgICAgICA+XG4gICAgICAgICAge1suLi5BcnJheSg2KV0ubWFwKChfLCBpKSA9PiAoXG4gICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICBrZXk9e2l9XG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgc2NhbGU6IDAsIHg6IDAsIHk6IDAgfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17e1xuICAgICAgICAgICAgICAgIHNjYWxlOiBbMCwgMSwgMF0sXG4gICAgICAgICAgICAgICAgeDogWzAsIChpICUgMiA/IDQwIDogLTQwKSAqIChpICsgMSkgLyAyXSxcbiAgICAgICAgICAgICAgICB5OiBbMCwgLTIwIC0gKGkgKiAxMCldLFxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7XG4gICAgICAgICAgICAgICAgZHVyYXRpb246IDIsXG4gICAgICAgICAgICAgICAgZGVsYXk6IDEuMiArIChpICogMC4yKSxcbiAgICAgICAgICAgICAgICByZXBlYXQ6IEluZmluaXR5LFxuICAgICAgICAgICAgICAgIHJlcGVhdERlbGF5OiAzXG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0xLzIgbGVmdC0xLzIgLXRyYW5zbGF0ZS14LTEvMiAtdHJhbnNsYXRlLXktMS8yXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFNwYXJrbGVzIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1wdXJwbGUtNDAwXCIgLz5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9tb3Rpb24uZGl2PlxuXG4gICAgICAgIHsvKiBGZWF0dXJlIEljb25zICovfVxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGRlbGF5OiAxLjUgfX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTQgbXQtMTJcIlxuICAgICAgICA+XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGFuaW1hdGU9e3sgeTogWzAsIC01LCAwXSB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMiwgcmVwZWF0OiBJbmZpbml0eSwgZGVsYXk6IDAgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXdoaXRlLzEwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC1sZyBwLTJcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxHbG9iZSBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtYmx1ZS00MDBcIiAvPlxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgYW5pbWF0ZT17eyB5OiBbMCwgLTUsIDBdIH19XG4gICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAyLCByZXBlYXQ6IEluZmluaXR5LCBkZWxheTogMC4zIH19XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZS8xMCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtbGcgcC0yXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8TGFuZ3VhZ2VzIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1wdXJwbGUtNDAwXCIgLz5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGFuaW1hdGU9e3sgeTogWzAsIC01LCAwXSB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMiwgcmVwZWF0OiBJbmZpbml0eSwgZGVsYXk6IDAuNiB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGUvMTAgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLWxnIHAtMlwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFZpZGVvIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1waW5rLTQwMFwiIC8+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgey8qIFRleHQgTGFiZWwgKi99XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwIH19XG4gICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogMiB9fVxuICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTYgdGV4dC1jZW50ZXJcIlxuICAgICAgICA+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+VHJhbnNmb3JtIFZpZGVvcyDihpIgU0VPIEFydGljbGVzPC9wPlxuICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn0iXSwibmFtZXMiOlsibW90aW9uIiwiVmlkZW8iLCJXYW5kMiIsIkZpbGVUZXh0IiwiU3BhcmtsZXMiLCJZb3V0dWJlIiwiR2xvYmUiLCJMYW5ndWFnZXMiLCJWaWRlb0FsY2hlbXlQcmV2aWV3IiwiZGl2IiwiY2xhc3NOYW1lIiwiYW5pbWF0ZSIsInNjYWxlIiwib3BhY2l0eSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsInJlcGVhdCIsIkluZmluaXR5IiwiZWFzZSIsImRlbGF5IiwiaW5pdGlhbCIsInJvdGF0ZSIsInR5cGUiLCJBcnJheSIsIm1hcCIsIl8iLCJpIiwieCIsInkiLCJyZXBlYXREZWxheSIsInAiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/VideoAlchemyPreview.tsx\n"));

/***/ })

}]);