"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/article-view/[id]/page",{

/***/ "(app-pages-browser)/./src/app/article-view/[id]/page.tsx":
/*!********************************************!*\
  !*** ./src/app/article-view/[id]/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ArticleViewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bookmark.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart,Bookmark,Brain,Calendar,Check,Clock,Copy,Crown,Download,ExternalLink,Eye,FileText,Globe,Heart,MessageCircle,Search,Shield,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ArticleViewPage() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams)();\n    const [article, setArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scores, setScores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [readingProgress, setReadingProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isBookmarked, setIsBookmarked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle scroll progress\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ArticleViewPage.useEffect\": ()=>{\n            const handleScroll = {\n                \"ArticleViewPage.useEffect.handleScroll\": ()=>{\n                    const totalHeight = document.documentElement.scrollHeight - window.innerHeight;\n                    const progress = window.scrollY / totalHeight * 100;\n                    setReadingProgress(Math.min(progress, 100));\n                }\n            }[\"ArticleViewPage.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"ArticleViewPage.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"ArticleViewPage.useEffect\"];\n        }\n    }[\"ArticleViewPage.useEffect\"], []);\n    // Handle authentication redirect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ArticleViewPage.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                router.push('/login');\n            }\n        }\n    }[\"ArticleViewPage.useEffect\"], [\n        status,\n        router\n    ]);\n    // Fetch article by ID\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ArticleViewPage.useEffect\": ()=>{\n            async function fetchArticle() {\n                if (status === 'loading' || !params.id) return;\n                if (status === 'unauthenticated') {\n                    router.push('/login');\n                    return;\n                }\n                try {\n                    setLoading(true);\n                    setError(null);\n                    const response = await fetch(\"/api/articles/\".concat(params.id));\n                    const data = await response.json();\n                    if (!response.ok) {\n                        throw new Error(data.error || 'Failed to fetch article');\n                    }\n                    if (data.success && data.article) {\n                        var _data_article_metadata;\n                        // Check if this is a YouTube script and redirect if so\n                        if (data.article.type === 'youtube_script') {\n                            // Redirect to YouTube script viewer\n                            const youtubeUrl = \"/youtube-script-view?script=\".concat(encodeURIComponent(data.article.content), \"&title=\").concat(encodeURIComponent(data.article.title));\n                            router.push(youtubeUrl);\n                            return;\n                        }\n                        setArticle(data.article);\n                        // Generate mock scores if metadata contains scoring info\n                        if ((_data_article_metadata = data.article.metadata) === null || _data_article_metadata === void 0 ? void 0 : _data_article_metadata.scores) {\n                            setScores(data.article.metadata.scores);\n                        } else {\n                            // Generate realistic mock scores based on article type\n                            const mockScores = generateMockScores(data.article.type, data.article.wordCount || 0);\n                            setScores(mockScores);\n                        }\n                    } else {\n                        throw new Error('Article not found');\n                    }\n                } catch (error) {\n                    console.error('Error fetching article:', error);\n                    setError(error instanceof Error ? error.message : 'Failed to load article');\n                    // Redirect to dashboard after showing error\n                    setTimeout({\n                        \"ArticleViewPage.useEffect.fetchArticle\": ()=>{\n                            router.push('/dashboard');\n                        }\n                    }[\"ArticleViewPage.useEffect.fetchArticle\"], 3000);\n                } finally{\n                    setLoading(false);\n                }\n            }\n            fetchArticle();\n        }\n    }[\"ArticleViewPage.useEffect\"], [\n        params.id,\n        status,\n        router\n    ]);\n    // Generate mock scores based on article type and quality\n    const generateMockScores = (type, wordCount)=>{\n        // Base scores with some variation\n        const baseScore = 75 + Math.random() * 20;\n        return {\n            seoScore: Math.min(95, baseScore + (type === 'blog' ? 10 : 5) + Math.random() * 10),\n            aeoScore: Math.min(95, baseScore + Math.random() * 15),\n            geoScore: Math.min(95, baseScore + Math.random() * 20),\n            readabilityScore: Math.min(95, baseScore + (wordCount > 500 ? 5 : -5) + Math.random() * 10),\n            uniquenessScore: Math.min(95, baseScore + Math.random() * 15),\n            externalLinkingScore: Math.min(95, baseScore + Math.random() * 25),\n            overallScore: Math.min(95, baseScore + Math.random() * 10),\n            recommendations: [\n                'Content shows excellent depth and research quality',\n                'Strong competitive advantage over existing articles',\n                'Human-like writing style detected',\n                'Good use of statistics and insights'\n            ]\n        };\n    };\n    const copyToClipboard = ()=>{\n        if (article) {\n            navigator.clipboard.writeText(article.content);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        }\n    };\n    const downloadArticle = ()=>{\n        if (article) {\n            const blob = new Blob([\n                article.content\n            ], {\n                type: 'text/markdown'\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"\".concat(article.title.replace(/\\s+/g, '-').toLowerCase(), \".md\");\n            a.click();\n            URL.revokeObjectURL(url);\n        }\n    };\n    const getScoreColor = (score)=>{\n        if (score >= 85) return 'from-emerald-500 to-green-500';\n        if (score >= 70) return 'from-blue-500 to-cyan-500';\n        if (score >= 50) return 'from-amber-500 to-orange-500';\n        return 'from-red-500 to-rose-500';\n    };\n    const getScoreGrade = (score)=>{\n        if (score >= 90) return 'A+';\n        if (score >= 85) return 'A';\n        if (score >= 80) return 'A-';\n        if (score >= 75) return 'B+';\n        if (score >= 70) return 'B';\n        if (score >= 65) return 'B-';\n        if (score >= 60) return 'C+';\n        if (score >= 55) return 'C';\n        return 'D';\n    };\n    // Loading state\n    if (status === 'loading' || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Loading article...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                lineNumber: 215,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n            lineNumber: 214,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-red-600/20 rounded-2xl mb-6 w-fit mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-8 h-8 text-red-400\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-white mb-2\",\n                        children: \"Article Not Found\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 mb-6\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        href: \"/dashboard\",\n                        className: \"inline-flex items-center px-6 py-3 bg-gradient-to-r from-violet-600 to-indigo-600 text-white rounded-xl hover:scale-105 transition-transform\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this),\n                            \"Go to Dashboard\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                lineNumber: 227,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n            lineNumber: 226,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render if not authenticated\n    if (status === 'unauthenticated' || !article) {\n        return null;\n    }\n    const wordCount = article.wordCount || article.content.split(/\\s+/).filter((word)=>word.length > 0).length;\n    const readingTime = Math.ceil(wordCount / 200);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-violet-900/10 via-black to-indigo-900/10\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        animate: {\n                            x: [\n                                0,\n                                100,\n                                0\n                            ],\n                            y: [\n                                0,\n                                -100,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 20,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-violet-500/10 rounded-full blur-[100px]\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        animate: {\n                            x: [\n                                0,\n                                -100,\n                                0\n                            ],\n                            y: [\n                                0,\n                                100,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 15,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-indigo-500/10 rounded-full blur-[120px]\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                className: \"fixed top-0 left-0 h-1 bg-gradient-to-r from-violet-600 to-indigo-600 z-50\",\n                style: {\n                    width: \"\".concat(readingProgress, \"%\")\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.header, {\n                initial: {\n                    opacity: 0,\n                    y: -20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"relative z-10 border-b border-white/10 backdrop-blur-xl bg-black/40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"flex items-center space-x-3 text-gray-400 hover:text-white transition-colors group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-5 h-5 group-hover:-translate-x-1 transition-transform\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Back to Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-px bg-white/20\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-xl blur-lg opacity-70\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative bg-black rounded-xl p-2.5 border border-white/20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-6 h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-xl font-bold text-white\",\n                                                        children: \"Superior Article\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: [\n                                                            article.type.charAt(0).toUpperCase() + article.type.slice(1),\n                                                            \" Content\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:flex items-center space-x-6 text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: new Date(article.createdAt).toLocaleDateString('en-US', {\n                                                            month: 'short',\n                                                            day: 'numeric',\n                                                            year: 'numeric'\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            readingTime,\n                                                            \" min read\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            wordCount.toLocaleString(),\n                                                            \" words\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-px bg-white/20\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsBookmarked(!isBookmarked),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"p-2.5 rounded-lg transition-colors\", isBookmarked ? 'bg-violet-600/20 text-violet-400' : 'text-gray-400 hover:text-white hover:bg-white/10'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-5 h-5 \".concat(isBookmarked ? 'fill-current' : '')\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsLiked(!isLiked),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"p-2.5 rounded-lg transition-colors\", isLiked ? 'bg-red-600/20 text-red-400' : 'text-gray-400 hover:text-white hover:bg-white/10'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-5 h-5 \".concat(isLiked ? 'fill-current' : '')\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: copyToClipboard,\n                                                className: \"flex items-center space-x-2 px-4 py-2.5 text-sm font-medium text-white bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg hover:bg-white/20 transition-all\",\n                                                children: [\n                                                    copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 29\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: copied ? 'Copied' : 'Copy'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                                onClick: downloadArticle,\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                className: \"flex items-center space-x-2 px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-violet-600 to-indigo-600 rounded-lg hover:shadow-lg transition-all\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Download\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"relative z-10 max-w-6xl mx-auto px-6 py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.1\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4 text-violet-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-200\",\n                                        children: \"AI Generated\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 text-yellow-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-3 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-violet-600/20 text-violet-300 border border-violet-500/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"AI Generated\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-emerald-600/20 text-emerald-300 border border-emerald-500/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"SEO Optimized\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-amber-600/20 text-amber-300 border border-amber-500/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Superior Quality\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-bold text-white leading-tight mb-6\",\n                                children: article.title\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center space-x-8 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-full blur-md opacity-70\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-12 h-12 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"w-6 h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-white\",\n                                                        children: \"Invincible AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: \"Content Agent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-px bg-white/20\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-6 text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            readingTime,\n                                                            \" min read\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            wordCount.toLocaleString(),\n                                                            \" words\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 9\n                    }, this),\n                    scores && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.2\n                        },\n                        className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-8 mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-5 h-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: \"Content Quality Analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Overall Grade:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-3xl font-bold bg-gradient-to-r \".concat(getScoreColor(scores.overallScore), \" bg-clip-text text-transparent\"),\n                                                children: getScoreGrade(scores.overallScore)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8\",\n                                children: [\n                                    {\n                                        label: 'SEO Score',\n                                        score: scores.seoScore,\n                                        icon: _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                                        color: 'violet'\n                                    },\n                                    {\n                                        label: 'AEO Score',\n                                        score: scores.aeoScore,\n                                        icon: _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                                        color: 'blue'\n                                    },\n                                    {\n                                        label: 'GEO Score',\n                                        score: scores.geoScore,\n                                        icon: _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                                        color: 'emerald'\n                                    },\n                                    {\n                                        label: 'Readability',\n                                        score: scores.readabilityScore,\n                                        icon: _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n                                        color: 'amber'\n                                    },\n                                    {\n                                        label: 'Uniqueness',\n                                        score: scores.uniquenessScore,\n                                        icon: _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                        color: 'pink'\n                                    },\n                                    {\n                                        label: 'External Links',\n                                        score: scores.externalLinkingScore,\n                                        icon: _barrel_optimize_names_ArrowLeft_Award_BarChart_Bookmark_Brain_Calendar_Check_Clock_Copy_Crown_Download_ExternalLink_Eye_FileText_Globe_Heart_MessageCircle_Search_Shield_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n                                        color: 'cyan'\n                                    }\n                                ].map((metric, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        initial: {\n                                            scale: 0,\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            scale: 1,\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            delay: 0.05 * idx\n                                        },\n                                        className: \"text-center p-4 rounded-xl bg-white/5 border border-white/10 hover:bg-white/10 transition-all\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-24 h-24 mx-auto transform -rotate-90\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"48\",\n                                                                cy: \"48\",\n                                                                r: \"40\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"6\",\n                                                                fill: \"none\",\n                                                                className: \"text-white/20\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"48\",\n                                                                cy: \"48\",\n                                                                r: \"40\",\n                                                                stroke: \"url(#gradient-\".concat(idx, \")\"),\n                                                                strokeWidth: \"6\",\n                                                                fill: \"none\",\n                                                                strokeDasharray: \"\".concat(metric.score / 100 * 251.33, \" 251.33\"),\n                                                                strokeLinecap: \"round\",\n                                                                className: \"transition-all duration-1000\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                                lineNumber: 504,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                    id: \"gradient-\".concat(idx),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"0%\",\n                                                                            stopColor: \"var(--\".concat(metric.color, \"-500)\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                                            lineNumber: 517,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"100%\",\n                                                                            stopColor: \"var(--\".concat(metric.color, \"-400)\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                                            lineNumber: 518,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 494,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-white\",\n                                                                    children: Math.round(metric.score)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: \"/ 100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                                    lineNumber: 525,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"inline-flex p-2 rounded-lg\", metric.color === 'violet' && \"bg-violet-600/20\", metric.color === 'blue' && \"bg-blue-600/20\", metric.color === 'emerald' && \"bg-emerald-600/20\", metric.color === 'amber' && \"bg-amber-600/20\", metric.color === 'pink' && \"bg-pink-600/20\", metric.color === 'cyan' && \"bg-cyan-600/20\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(metric.icon, {\n                                                            className: \"w-5 h-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-semibold text-white\",\n                                                        children: metric.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: metric.score >= 85 ? 'Excellent' : metric.score >= 70 ? 'Good' : metric.score >= 50 ? 'Fair' : 'Needs Work'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, metric.label, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.3\n                        },\n                        className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"prose prose-invert prose-lg max-w-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_27__.Markdown, {\n                                remarkPlugins: [\n                                    remark_gfm__WEBPACK_IMPORTED_MODULE_28__[\"default\"]\n                                ],\n                                components: {\n                                    h1: (param)=>{\n                                        let { children } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl font-bold text-white mb-6\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 39\n                                        }, void 0);\n                                    },\n                                    h2: (param)=>{\n                                        let { children } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold text-white mb-4 mt-8\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 39\n                                        }, void 0);\n                                    },\n                                    h3: (param)=>{\n                                        let { children } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-white mb-3 mt-6\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 39\n                                        }, void 0);\n                                    },\n                                    p: (param)=>{\n                                        let { children } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 mb-4 leading-relaxed\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 38\n                                        }, void 0);\n                                    },\n                                    a: (param)=>{\n                                        let { href, children } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: href,\n                                            className: \"text-violet-400 hover:text-violet-300 underline\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 568,\n                                            columnNumber: 19\n                                        }, void 0);\n                                    },\n                                    ul: (param)=>{\n                                        let { children } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-gray-300 mb-6 ml-6 space-y-3 list-disc list-outside\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 19\n                                        }, void 0);\n                                    },\n                                    ol: (param)=>{\n                                        let { children } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                            className: \"text-gray-300 mb-6 ml-6 space-y-3 list-decimal list-outside\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 19\n                                        }, void 0);\n                                    },\n                                    li: (param)=>{\n                                        let { children } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"text-gray-300 leading-relaxed\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 19\n                                        }, void 0);\n                                    },\n                                    blockquote: (param)=>{\n                                        let { children } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                            className: \"border-l-4 border-violet-500 pl-4 italic text-gray-400 my-6\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 19\n                                        }, void 0);\n                                    },\n                                    code: (param)=>{\n                                        let { children } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"bg-white/10 px-2 py-1 rounded text-violet-300 font-mono text-sm\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 19\n                                        }, void 0);\n                                    },\n                                    table: (param)=>{\n                                        let { children } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"overflow-x-auto my-8 rounded-2xl shadow-2xl border border-white/20 bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                className: \"min-w-full border-collapse\",\n                                                children: children\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 19\n                                        }, void 0);\n                                    },\n                                    thead: (param)=>{\n                                        let { children } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gradient-to-r from-blue-600/20 to-purple-600/20 border-b border-white/20\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 19\n                                        }, void 0);\n                                    },\n                                    tbody: (param)=>{\n                                        let { children } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"divide-y divide-white/10\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 19\n                                        }, void 0);\n                                    },\n                                    tr: (param)=>{\n                                        let { children } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"hover:bg-white/5 transition-all duration-200\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 19\n                                        }, void 0);\n                                    },\n                                    th: (param)=>{\n                                        let { children } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-4 text-left font-semibold text-white border-r border-white/10 last:border-r-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                children\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 19\n                                        }, void 0);\n                                    },\n                                    td: (param)=>{\n                                        let { children } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 text-gray-200 border-r border-white/10 last:border-r-0 font-medium\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 19\n                                        }, void 0);\n                                    }\n                                },\n                                children: article.content\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                                lineNumber: 560,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                            lineNumber: 559,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                        lineNumber: 553,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n                lineNumber: 392,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/[id]/page.tsx\",\n        lineNumber: 254,\n        columnNumber: 5\n    }, this);\n}\n_s(ArticleViewPage, \"jALbOm4gNPnVnJLS+qVRbbPTc3U=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams\n    ];\n});\n_c = ArticleViewPage;\nvar _c;\n$RefreshReg$(_c, \"ArticleViewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/article-view/[id]/page.tsx\n"));

/***/ })

});